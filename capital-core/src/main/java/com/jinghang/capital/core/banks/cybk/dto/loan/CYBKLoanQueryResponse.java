package com.jinghang.capital.core.banks.cybk.dto.loan;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKLoanQueryResponse {

    /**
     * 外部放款流水号
     */
    private String outLoanSeq;
    /**
     * 长银放款流水号
     */
    private String loanSeq;
    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 放款状态
     */
    private String dnSts;
    /**
     * 放款金额
     */
    private String dnAmt;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 长银借据号
     */
    private String loanNo;
    /**
     * 放款日期
     */
    private String loanActvDt;
    /**
     * 放款时间
     */
    private String loanActvTime;
    /**
     * 放款描述
     */
    private String payMsg;
    /**
     * 担保公司码值
     */
    private String guarCompanyCode;
    /**
     * 担保公司名称
     */
    private String guarCompanyName;

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getDnSts() {
        return dnSts;
    }

    public void setDnSts(String dnSts) {
        this.dnSts = dnSts;
    }

    public String getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(String dnAmt) {
        this.dnAmt = dnAmt;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getLoanActvDt() {
        return loanActvDt;
    }

    public void setLoanActvDt(String loanActvDt) {
        this.loanActvDt = loanActvDt;
    }

    public String getLoanActvTime() {
        return loanActvTime;
    }

    public void setLoanActvTime(String loanActvTime) {
        this.loanActvTime = loanActvTime;
    }

    public String getPayMsg() {
        return payMsg;
    }

    public void setPayMsg(String payMsg) {
        this.payMsg = payMsg;
    }

    public String getGuarCompanyCode() {
        return guarCompanyCode;
    }

    public void setGuarCompanyCode(String guarCompanyCode) {
        this.guarCompanyCode = guarCompanyCode;
    }

    public String getGuarCompanyName() {
        return guarCompanyName;
    }

    public void setGuarCompanyName(String guarCompanyName) {
        this.guarCompanyName = guarCompanyName;
    }
}
