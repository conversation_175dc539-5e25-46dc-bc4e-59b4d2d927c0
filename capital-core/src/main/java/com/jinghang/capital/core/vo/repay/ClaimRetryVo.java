package com.jinghang.capital.core.vo.repay;


import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.enums.BankChannel;
import java.time.LocalDate;

public class ClaimRetryVo {

    /**
     * 代偿日
     */
    private LocalDate claimDay;
    /**
     * 资方
     */
    private BankChannel bankChannel;

    private GuaranteeCompany guaranteeCompany;

    public LocalDate getClaimDay() {
        return claimDay;
    }

    public void setClaimDay(LocalDate claimDay) {
        this.claimDay = claimDay;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }
}
