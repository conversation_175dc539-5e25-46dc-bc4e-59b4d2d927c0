package com.jinghang.capital.core.banks.cybk.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/23
 */
public enum CYBKCheckState {
    AVAILABLE("01", "可授信"),
    THIS_CHANNEL_ALREADY("02", "本渠道已授信"),
    OTHER_CHANNEL_ALREADY("03", "其它渠道已授信"),
    REFUSE("00", "拒绝");

    private String code;
    private String desc;

    CYBKCheckState(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public CYBKCheckState findByCode(String code) {
        CYBKCheckState[] states = CYBKCheckState.values();
        for (CYBKCheckState state : states) {
            if (state.getCode().equals(code)) {
                return state;
            }
        }
        throw new RuntimeException("长银直连授信准入状态未找到");
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return desc;
    }

    public static CYBKCheckState getEnumByCode(String code) {
        return Arrays.stream(values()).filter(c -> code.equals(c.code)).findFirst().orElse(CYBKCheckState.REFUSE);
    }

    public static boolean isRefuse(CYBKCheckState state) {
        return OTHER_CHANNEL_ALREADY.equals(state) || REFUSE.equals(state);
    }

    public static boolean isRefuse(String code) {
        return OTHER_CHANNEL_ALREADY.code.equals(code) || REFUSE.code.equals(code);
    }


    public static boolean isAvailable(String code) {
        return AVAILABLE.code.equals(code) || THIS_CHANNEL_ALREADY.code.equals(code);
    }

}
