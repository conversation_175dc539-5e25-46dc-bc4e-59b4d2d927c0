package com.jinghang.capital.core.banks.cybk.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/27
 */
public enum CYBKMonthIncome {

    ONE("1", 0, 5000),
    TWO("2", 5000, 7500),
    THREE("3", 7500, 10000),
    FOUR("4", 10000, 15000),
    FIVE("5", 15000, 20000),
    SIX("6", 20000, 30000),
    SEVEN("7", 30000, 40000),
    EIGHT("8", 40000, 50000),
    NINE("9", 50000, 100000),
    TEN("10", 100000, *********);

    private final String code;
    private final int low;
    private final int high;

    CYBKMonthIncome(String code, int low, int high) {
        this.code = code;
        this.low = low;
        this.high = high;
    }

    public static String getCodeByIncome(int income) {
        return Arrays.stream(values()).filter(l -> income >= l.low && income < l.high).findFirst().orElseThrow().getCode();
    }


    public String getCode() {
        return code;
    }

    public int getLow() {
        return low;
    }

    public int getHigh() {
        return high;
    }
}
