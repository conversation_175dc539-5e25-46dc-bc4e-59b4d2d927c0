package com.jinghang.capital.core.vo.repay;


import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayType;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
public class RepayTrailVo {
    /**
     * 业务系统放款ID
     */
    private String outerLoanId;
    /**
     * 资金系统放款ID
     */
    private String loanId;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 还款模式
     */
    private RepayPurpose repayPurpose;
    /**
     * 还款类型
     */
    private RepayType repayType;
    /**
     * 融担费
     */
    private BigDecimal guaranteeFee;


    /**
     * 转账时间（只有线下还款时需要传）
     */
    private LocalDate transferDate;

    /**
     * 是否逾期
     */
    private String isOverdue;

    public LocalDate getTransferDate() {
        return transferDate;
    }

    public void setTransferDate(LocalDate transferDate) {
        this.transferDate = transferDate;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOuterLoanId() {
        return outerLoanId;
    }

    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public String getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(String isOverdue) {
        this.isOverdue = isOverdue;
    }

}
