package com.jinghang.capital.core.vo.file;


import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.vo.ProductVo;

/**
 * <AUTHOR>
 * @date 2023/5/13
 */
public class FileUploadVo {
    /**
     * 协议文件合作方提现单号, (对账文件不需要)
     */
    private String loanOrderId;

    /**
     * 放款合同
     * 放款对账文件
     * 还款对账文件
     * 代偿对账文件
     * 回购对账文件
     */
    private FileType type;

    /**
     * 文件名(下载对账文件时使用)
     */
    private String fileName;


    private String bucketName;

    private String fileKey;

    /**
     * 产品
     */
    private ProductVo product;
    /**
     * 银行
     */
    private BankChannel bankChannel;

    /**
     * 富民银行-用户编号
     */
    private String userId;
    /**
     * 富民银行-流水号
     */
    private String processNo;
    /**
     * 富民银行-文件场景
     */
    private String scene;

    public String getProcessNo() {
        return processNo;
    }

    public void setProcessNo(String processNo) {
        this.processNo = processNo;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoanOrderId() {
        return loanOrderId;
    }

    public void setLoanOrderId(String loanOrderId) {
        this.loanOrderId = loanOrderId;
    }

    public FileType getType() {
        return type;
    }

    public void setType(FileType type) {
        this.type = type;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public ProductVo getProduct() {
        return product;
    }

    public void setProduct(ProductVo product) {
        this.product = product;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getFileKey() {
        return fileKey;
    }

    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }
}
