package com.jinghang.capital.core.banks.cybk.recc;


import com.jinghang.capital.core.banks.cybk.enums.CYBKReccFileType;
import com.jinghang.capital.core.entity.BankLoanReplan;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.CYBKReccClaim;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.enums.RepayType;
import com.jinghang.capital.core.vo.recc.ReccType;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class CYBKReccClaimHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKReccAbstractHandler.class);

    @Override
    public void process(LocalDate reccDay) {
        CYBKReconcileFile reconcileFile = findReconcileFile(reccDay, CYBKReccFileType.COMPENSATION_FILE);
        reconcileFile.setReccDate(LocalDate.now());

        String reccId = reconcileFile.getId();
        List<CYBKReccClaim> reccClaimFileRecords = findReccClaimFileRecords(reccId);

        reccClaimFileRecords.forEach(lf -> {
            updateBankPlanInter(lf);
            createBankRecordInter(lf);

            lf.setReccStatus(ReccStateEnum.S.name());
            updateReccClaimStatus(lf);
        });

        reconcileFile.setReccState(ReccStateEnum.S.name());
        updateCYBKReconcileFile(reconcileFile);
    }

    private void updateBankPlanInter(CYBKReccClaim reccClaim) {
        var loanId = reccClaim.getSysId();
        var period = reccClaim.getPeriod();
        BankLoanReplan bankPlan = findBankPlan(loanId, period);
        if (bankPlan == null) {
            bankPlan = new BankLoanReplan();
            bankPlan.setChannel(BankChannel.CYBK);
            bankPlan.setLoanId(reccClaim.getSysId());
            bankPlan.setPeriod(reccClaim.getPeriod());
            bankPlan.setRepayStatus(RepayStatus.REPAID);
            bankPlan.setRepayType(RepayType.CLAIM);
            bankPlan.setRepayMode(RepayMode.OFFLINE);
            if ("01".equals(reccClaim.getRepayMode())) {
                bankPlan.setRepayPurpose(RepayPurpose.CURRENT);
            } else if ("02".equals(reccClaim.getRepayMode())) {
                bankPlan.setRepayPurpose(RepayPurpose.CLEAR);
            }
        }
        bankPlan.setActTotalAmt(reccClaim.getAmount());
        bankPlan.setActPrincipalAmt(reccClaim.getPrincipalAmt());
        bankPlan.setActInterestAmt(reccClaim.getInterestAmt());
        bankPlan.setActGuaranteeAmt(BigDecimal.ZERO);
        bankPlan.setActPenaltyAmt(reccClaim.getPenaltyAmt());
        bankPlan.setActBreachAmt(BigDecimal.ZERO);
        bankPlan.setActRepayTime(LocalDateTime.now());

        updateBankPlan(bankPlan);
    }

    private void createBankRecordInter(CYBKReccClaim reccClaim) {

        BankRepayRecord record = new BankRepayRecord();
        record.setChannel(BankChannel.CYBK);
        record.setLoanId(reccClaim.getSysId());
        record.setPeriod(reccClaim.getPeriod());
        record.setRepayStatus(ProcessStatus.SUCCESS);
        record.setRepayTime(LocalDateTime.now());

        record.setTotalAmt(reccClaim.getAmount());
        record.setPrincipalAmt(reccClaim.getPrincipalAmt());
        record.setInterestAmt(reccClaim.getInterestAmt());
        record.setGuaranteeAmt(BigDecimal.ZERO);
        record.setPenaltyAmt(reccClaim.getPenaltyAmt());
        record.setBreachAmt(BigDecimal.ZERO);
        record.setRepayType(RepayType.CLAIM);
        record.setRepayMode(RepayMode.OFFLINE);

        if ("01".equals(reccClaim.getRepayMode())) {
            record.setRepayPurpose(RepayPurpose.CURRENT);
        } else if ("02".equals(reccClaim.getRepayMode())) {
            record.setRepayPurpose(RepayPurpose.CLEAR);
        }
        updateBankRepayRecord(record);

    }


    @Override
    public ReccType getReccType() {
        return ReccType.COMPENSATION;
    }
}
