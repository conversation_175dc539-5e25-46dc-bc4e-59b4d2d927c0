import request from '@/utils/request'

// 投诉/备注查询
export function complaintNote(data) {
  return request({
    url: 'customer/complaintNote',
    method: 'post',
    data
  })
}

// 查询客户投诉
export function queryComplaint(data) {
  return request({
    url: 'customer/queryComplaint',
    method: 'post',
    data
  })
}

// 客户投诉编辑
export function updateComplaint(data) {
  return request({
    url: 'customer/updateComplaint',
    method: 'post',
    data
  })
}

// 客户投诉新增
export function addComplaint(data) {
  return request({
    url: 'customer/addComplaint',
    method: 'post',
    data
  })
}

// 客户投诉导出
export function exportComplaint(data) {
  return request({
    url: 'customer/exportComplaint',
    method: 'post',
    data,
    responseType: "arraybuffer",
  })
}

// 查询订单备注
export function queryOrderRemark(data) {
  return request({
    url: 'customer/queryOrderRemark',
    method: 'post',
    data
  })
}

// 订单备注新增
export function addOrderRemark(data) {
  return request({
    url: 'customer/addOrderRemark',
    method: 'post',
    data
  })
}

// 订单备注编辑
export function updateOrderRemark(data) {
  return request({
    url: 'customer/updateOrderRemark',
    method: 'post',
    data
  })
}

// 订单备注导出
export function exportOrderRemark(data) {
  return request({
    url: 'customer/exportOrderRemark',
    method: 'post',
    data,
    responseType: "arraybuffer",
  })
}

