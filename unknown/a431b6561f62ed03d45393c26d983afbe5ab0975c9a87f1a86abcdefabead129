package com.jinghang.capital.core.banks.cybk.dto.bind;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


/**
 * 银行卡变更
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKCardChangeRequest extends CYBKBaseRequest {

    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.BANK_CARD_CHANGE;


    /**
     * 外部账户变更流水号
     */
    private String outAcctChgSeq;
    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 长银借据号
     */
    private String loanNo;
    /**
     * 借款人证件类型
     */
    private String idTyp;
    /**
     * 借款人证件号
     */
    private String idNo;
    /**
     * 借款人姓名
     */
    private String acctName;
    /**
     * 账号类别
     */
    private String acctKind;
    /**
     * 账号类型
     */
    private String acctTyp;
    /**
     * 原帐号银行代码
     */
    private String oldAcctBankCde;
    /**
     * 原账号
     */
    private String oldAcctNo;
    /**
     * 原帐号预留电话号码
     */
    private String oldAcctPhone;
    /**
     * 新帐号银行代码
     */
    private String newAcctBankCde;
    /**
     * 新账号
     */
    private String newAcctNo;
    /**
     * 新帐号预留电话号码
     */
    private String newAcctPhone;
    /**
     * 签约授权号
     */
    private String agrSeq;
    /**
     * 卡号变更协议文件路径
     */
    private String acctChangeUrl;

    public String getOutAcctChgSeq() {
        return outAcctChgSeq;
    }

    public void setOutAcctChgSeq(String outAcctChgSeq) {
        this.outAcctChgSeq = outAcctChgSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getAcctKind() {
        return acctKind;
    }

    public void setAcctKind(String acctKind) {
        this.acctKind = acctKind;
    }

    public String getAcctTyp() {
        return acctTyp;
    }

    public void setAcctTyp(String acctTyp) {
        this.acctTyp = acctTyp;
    }

    public String getOldAcctBankCde() {
        return oldAcctBankCde;
    }

    public void setOldAcctBankCde(String oldAcctBankCde) {
        this.oldAcctBankCde = oldAcctBankCde;
    }

    public String getOldAcctNo() {
        return oldAcctNo;
    }

    public void setOldAcctNo(String oldAcctNo) {
        this.oldAcctNo = oldAcctNo;
    }

    public String getOldAcctPhone() {
        return oldAcctPhone;
    }

    public void setOldAcctPhone(String oldAcctPhone) {
        this.oldAcctPhone = oldAcctPhone;
    }

    public String getNewAcctBankCde() {
        return newAcctBankCde;
    }

    public void setNewAcctBankCde(String newAcctBankCde) {
        this.newAcctBankCde = newAcctBankCde;
    }

    public String getNewAcctNo() {
        return newAcctNo;
    }

    public void setNewAcctNo(String newAcctNo) {
        this.newAcctNo = newAcctNo;
    }

    public String getNewAcctPhone() {
        return newAcctPhone;
    }

    public void setNewAcctPhone(String newAcctPhone) {
        this.newAcctPhone = newAcctPhone;
    }

    public String getAgrSeq() {
        return agrSeq;
    }

    public void setAgrSeq(String agrSeq) {
        this.agrSeq = agrSeq;
    }

    public String getAcctChangeUrl() {
        return acctChangeUrl;
    }

    public void setAcctChangeUrl(String acctChangeUrl) {
        this.acctChangeUrl = acctChangeUrl;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }
}
