# 长银资方定价利率改造分析报告

## 概述

长银资方要求利率要根据用信接口返回的定价利率参数进行计算，之前是固定使用23.99%作为对资利率，现在需要用接口返回的定价利率进行动态计算。

## 1. 之前利率来源分析

### 1.1 利率的初始设置来源

#### 1.1.1 QhBank枚举中的固定配置（对资利率来源）

**位置**: `src/main/java/com/maguo/loan/cash/flow/enums/QhBank.java`

```java
CYBK(new BigDecimal("0.239900"), new BigDecimal("0.1500"), BankChannel.CYBK, BindSignMode.SHARE, false, "长银消金", 5),
```

- `bankCustomRate`: 23.99% - 银行对客户的利率
- `bankRate`: **15%** - **银行合同利率（对资利率）** ⭐ **这是之前固定对资利率的来源**

#### 1.1.2 RateLevel枚举中的固定利率（对客利率来源）

**位置**: `src/main/java/com/maguo/loan/cash/flow/enums/RateLevel.java`

```java
RATE_24(new BigDecimal("0.2399")),  // 23.99% - 权益客户
RATE_36(new BigDecimal("0.3599")),  // 35.99% - 普通客户
```

#### 1.1.3 利率传递链路

1. **风控审批阶段**: `RiskResultEventListener` 设置审批利率
   ```java
   RateLevel approveRate = record.getApproveRate();
   order.setApproveRate(approveRate);
   order.setIrrRate(approveRate.getRate()); // 设置对客利率
   ```

2. **授信初始化**: `CreditService.initCredit()` 从Order获取利率
   ```java
   credit.setCustomRate(order.getIrrRate()); // 对客利率
   credit.setBankRate(qhBank.getBankRate()); // 对资利率 = 15%
   ```

3. **用信申请**: `CYBKLoanService.buildApplyRequest()` 使用固定利率
   ```java
   request.setPriceIntRat(loan.getBankRate()); // 使用固定的15%
   request.setCustDayRate(loan.getCustomRate()); // 对客利率
   ```

## 2. 各环节利率使用详细分析

### 2.1 授信环节

#### 2.1.1 之前的做法
- **利率来源**: QhBank枚举固定配置
- **设置位置**: `CreditService.initCredit()`
- **具体代码**:
  ```java
  QhBank qhBank = QhBank.getQhBankBy(order.getBankChannel());
  credit.setBankRate(qhBank.getBankRate()); // 固定15%
  credit.setCustomRate(order.getIrrRate()); // 从RateLevel获取
  ```

#### 2.1.2 现在需要改为
- **利率来源**: 长银额度查询接口返回的定价利率
- **接口字段**: `CYBKLimitQueryResponse.priceIntRate`
- **改造点**: 授信成功后调用额度查询接口获取定价利率

### 2.2 用信环节

#### 2.2.1 之前的做法
- **利率来源**: 从Credit实体的bankRate字段获取（固定15%）
- **设置位置**: `CYBKLoanService.buildApplyRequest()`
- **具体代码**:
  ```java
  request.setPriceIntRat(loan.getBankRate()); // 使用固定的15%
  request.setCustDayRate(loan.getCustomRate()); // 对客利率
  ```

#### 2.2.2 现在需要改为
- **利率来源**: 从Credit实体获取动态定价利率
- **改造点**: 使用授信阶段获取的定价利率

### 2.3 还款计划生成环节

#### 2.3.1 之前的做法
- **对资利率计算**: 使用固定的bankRate
- **具体位置**: `CoreRepayPlanCalculator.calculate()`
- **代码示例**:
  ```java
  List<RepayPlan> bankPlanList = PlanGenerator.genPlan(
      InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST,
      repayDate, loanAmt, bankRate, periods); // bankRate = 15%
  ```

#### 2.3.2 现在需要改为
- **对资利率计算**: 使用动态定价利率
- **改造点**: bankRate字段存储动态定价利率

### 2.4 还款试算环节

#### 2.4.1 之前的做法
- **融担费计算**: `Rate36Service.trialClearGuaranteeAmt()`
- **利率获取**: `qhBank.getBankCustomRate().subtract(qhBank.getBankRate())`
- **具体代码**:
  ```java
  QhBank qhBank = QhBank.getQhBankBy(loan.getBankChannel());
  BigDecimal guaranteeRate = qhBank.getBankCustomRate().subtract(qhBank.getBankRate());
  // guaranteeRate = 23.99% - 15% = 8.99%
  ```

#### 2.4.2 现在需要改为
- **融担费计算**: 使用动态定价利率
- **改造点**: `guaranteeRate = 对客利率 - 动态定价利率`

### 2.5 协议生成环节

#### 2.5.1 之前的做法
- **协议参数**: `AgreementService.buildParams()`
- **利率字段**: 从Loan实体获取固定利率
- **具体使用**: 协议模板中的利率相关字段

#### 2.5.2 现在需要改为
- **协议参数**: 使用动态定价利率
- **改造点**: 确保协议中的利率字段使用正确的动态利率

## 3. 长银接口中的定价利率字段

### 3.1 额度查询接口响应中的定价利率

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/dto/limit/CYBKLimitQueryResponse.java`

```java
/** 长银定价利率，16位数字，9位小数，表示年化利率 */
private BigDecimal priceIntRate; ⭐ 这是长银返回的动态定价利率

/** 对客展示利率，16位数字，9位小数 */
private BigDecimal custShowRate;
```

### 3.2 用信申请请求中的利率字段

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/dto/loan/CYBKLoanApplyRequest.java`

```java
/**
 * 利率 - 需要设置为动态定价利率
 */
private BigDecimal priceIntRat;

/**
 * 客户展示利率
 */
private BigDecimal custDayRate;
```

## 4. 具体改造点对比

### 4.1 授信流程改造

#### 4.1.1 之前的流程
```
授信申请 → 授信成功 → 设置固定利率(15%) → 存储到Credit.bankRate
```

#### 4.1.2 改造后的流程
```
授信申请 → 授信成功 → 调用额度查询接口 → 获取priceIntRate → 存储到Credit.bankRate
```

**改造位置**: `CYBKCreditService.java`
- 授信成功回调处理
- 授信结果查询处理

### 4.2 用信流程改造

#### 4.2.1 之前的流程
```java
// CYBKLoanService.buildApplyRequest()
request.setPriceIntRat(loan.getBankRate()); // 固定15%
```

#### 4.2.2 改造后的流程
```java
// CYBKLoanService.buildApplyRequest()
Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
request.setPriceIntRat(credit.getBankRate()); // 动态定价利率
```

### 4.3 利率计算服务改造

#### 4.3.1 之前的计算方式
```java
// Rate36Service.trialClearGuaranteeAmt()
QhBank qhBank = QhBank.getQhBankBy(loan.getBankChannel());
BigDecimal guaranteeRate = qhBank.getBankCustomRate().subtract(qhBank.getBankRate());
// guaranteeRate = 23.99% - 15% = 8.99%
```

#### 4.3.2 改造后的计算方式
```java
// Rate36Service.trialClearGuaranteeAmt()
BigDecimal guaranteeRate = loan.getCustomRate().subtract(loan.getBankRate());
// guaranteeRate = 对客利率 - 动态定价利率
```

### 4.4 LPR查询改造

#### 4.4.1 之前的方式
```java
// CYBKLoanService.lpr()
lprQueryRequest.setPriceRate(credit.getBankRate()); // 固定15%
```

#### 4.4.2 改造后的方式
```java
// CYBKLoanService.lpr()
lprQueryRequest.setPriceRate(credit.getBankRate()); // 动态定价利率
```

## 5. 需要修改的关键代码位置

### 5.1 授信相关

| 文件位置 | 方法 | 当前逻辑 | 改造后逻辑 |
|---------|------|---------|-----------|
| `CYBKCreditService.java` | 授信成功处理 | 无额度查询 | 调用额度查询获取定价利率 |
| `CreditService.initCredit()` | 初始化授信 | `credit.setBankRate(qhBank.getBankRate())` | `credit.setBankRate(动态定价利率)` |

### 5.2 用信相关

| 文件位置 | 方法 | 当前逻辑 | 改造后逻辑 |
|---------|------|---------|-----------|
| `CYBKLoanService.buildApplyRequest()` | 构建用信请求 | `request.setPriceIntRat(loan.getBankRate())` | 使用Credit中的动态定价利率 |
| `CYBKLoanService.bankLoanApplyResult()` | 用信结果处理 | 无利率更新 | 将定价利率同步到Loan.bankRate |

### 5.3 还款计划相关

| 文件位置 | 方法 | 当前逻辑 | 改造后逻辑 |
|---------|------|---------|-----------|
| `CoreRepayPlanCalculator.calculate()` | 还款计划计算 | 使用固定bankRate | 使用动态定价利率 |
| `CYBKLoanService.getGuaranteeAmtAndRate()` | 担保费计算 | 使用固定利率差 | 使用动态利率差 |

### 5.4 还款试算相关

| 文件位置 | 方法 | 当前逻辑 | 改造后逻辑 |
|---------|------|---------|-----------|
| `Rate36Service.trialClearGuaranteeAmt()` | 试算结清担保费 | `qhBank.getBankRate()` | `loan.getBankRate()` |
| `TrialService.repayTrial()` | 还款试算 | 使用固定利率 | 使用动态利率 |

### 5.5 协议相关

| 文件位置 | 方法 | 当前逻辑 | 改造后逻辑 |
|---------|------|---------|-----------|
| `AgreementService.buildParams()` | 协议参数构建 | 使用固定利率 | 使用动态利率 |
| 协议模板 | 利率字段填充 | 固定利率值 | 动态利率值 |

## 6. 改造实施方案

### 6.1 获取定价利率的时机

1. **授信成功后**: 从额度查询接口(`CYBKLimitQueryResponse`)获取`priceIntRate`
2. **用信申请时**: 使用获取到的定价利率设置到`CYBKLoanApplyRequest.priceIntRat`
3. **用信成功后**: 将定价利率存储到借据表的`bankRate`字段

### 6.2 数据存储策略

- **Credit表**: 使用现有的`bankRate`字段存储授信时的定价利率
- **Loan表**: 使用现有的`bankRate`字段存储用信时的定价利率
- **向后兼容**: 历史数据保持原有的15%利率不变

## 7. 关键改造点总结

### 7.1 利率来源变化

| 环节 | 之前来源 | 现在来源 | 具体字段 |
|------|---------|---------|----------|
| 授信 | QhBank.bankRate (15%) | 长银额度查询接口 | CYBKLimitQueryResponse.priceIntRate |
| 用信 | Credit.bankRate (15%) | Credit.bankRate (动态) | 从授信阶段获取的定价利率 |
| 还款计划 | 固定15% | Loan.bankRate (动态) | 用信成功后存储的定价利率 |
| 还款试算 | QhBank.bankRate (15%) | Loan.bankRate (动态) | 从借据获取的动态利率 |
| 协议生成 | 固定利率 | 动态利率 | 从相关实体获取动态利率 |

### 7.2 数据流转链路

#### 7.2.1 之前的数据流转
```
QhBank枚举(15%) → Credit.bankRate → Loan.bankRate → 各种计算
```

#### 7.2.2 改造后的数据流转
```
长银额度查询接口 → priceIntRate → Credit.bankRate → Loan.bankRate → 各种计算
```

### 7.3 核心改造文件清单

1. **CYBKCreditService.java** - 授信成功后调用额度查询
2. **CYBKLoanService.java** - 用信申请使用动态利率
3. **Rate36Service.java** - 试算计算使用动态利率
4. **CoreRepayPlanCalculator.java** - 还款计划使用动态利率
5. **AgreementService.java** - 协议参数使用动态利率

## 8. 实施建议

### 8.1 实施步骤

1. **第一步**: 修改授信流程，获取并存储定价利率
2. **第二步**: 修改用信流程，使用动态定价利率
3. **第三步**: 修改利率计算相关服务
4. **第四步**: 修改协议生成相关逻辑
5. **第五步**: 全面测试验证

### 8.2 风险控制

1. **向后兼容**: 确保历史数据的利率计算不受影响
2. **异常处理**: 当无法获取定价利率时的降级策略（使用固定15%）
3. **数据一致性**: 确保各个环节使用的利率保持一致
4. **灰度发布**: 建议先在测试环境充分验证后再上线

### 8.3 测试重点

1. **授信流程**: 验证定价利率的获取和存储
2. **用信流程**: 验证定价利率的使用
3. **利率计算**: 验证融担费、咨询费等各种利率计算场景
4. **还款试算**: 验证试算结果的准确性
5. **协议生成**: 验证协议中利率字段的正确性
6. **边界情况**: 验证异常情况的处理

## 9. 总结

本次改造将长银资方的固定利率(15%)改为动态获取的定价利率，主要涉及：

- **5个核心环节**: 授信、用信、还款计划、还款试算、协议生成
- **关键改造点**: 从固定利率配置改为接口动态获取
- **数据流转**: 建立从长银接口到各业务环节的利率传递链路
- **向后兼容**: 确保历史数据和异常情况的处理

需要仔细处理各个环节的数据传递和存储，确保利率的一致性和准确性。
