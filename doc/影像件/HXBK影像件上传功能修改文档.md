# HXBK授信资料上传功能修改文档

## 📋 修改概述

根据用户需求，修改了HXBK授信申请中资料上传功能的实现：

1. **meterialName** 应该是完整的文件名（如 `IDCARD-*****************.zip`）
2. **file_path** 应该使用上传时实际生成的完整路径，而不是重新组装

## 🔧 主要修改

### 1. 修改HXBKImageFileService.uploadImageFilesToAntSftp方法

**文件位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/hxbk/service/HXBKImageFileService.java`

#### 修改前后对比：

**修改前**：
```java
// 上传影像件到蚂蚁SFTP
hxbkImageFileService.uploadImageFilesToAntSftp(credit, account, imageFileList);

// 构建资料列表
List<HXBKMaterial> materials = buildMaterialList(targetImageFiles, applySerialNo, idCardNo);
return materials;
```

**修改后**：
```java
// 用于收集上传后的文件信息
List<HXBKMaterial> materials = new ArrayList<>();

// 上传身份证影像件并收集文件信息
if (!idCardFiles.isEmpty()) {
    List<HXBKMaterial> idCardMaterials = uploadIdCardImagesAndBuildMaterials(idCardFiles, applySerialNo, idCardNo);
    materials.addAll(idCardMaterials);
}

// 上传人脸影像件并收集文件信息
if (!faceFiles.isEmpty()) {
    List<HXBKMaterial> faceMaterials = uploadFaceImagesAndBuildMaterials(faceFiles, applySerialNo);
    materials.addAll(faceMaterials);
}

return materials;
```

### 2. 新增uploadIdCardImagesAndBuildMaterials方法

将原来的 `uploadIdCardImages` 方法修改为 `uploadIdCardImagesAndBuildMaterials`，在上传完成后构建资料信息：

```java
private List<HXBKMaterial> uploadIdCardImagesAndBuildMaterials(List<LoanFile> idCardFiles, String applySerialNo, String idCardNo) {
    // ... 原有上传逻辑 ...
    
    // 上传到SFTP
    String remoteDir = hxbkConfig.getIdCardDir() + "/" + currentDate;
    uploadToSftp(zipFilePath, remoteDir, zipFileName);
    uploadToSftp(indexFilePath, remoteDir, indexFileName);
    
    // 构建身份证资料信息
    List<HXBKMaterial> materials = new ArrayList<>();
    String filePath = remoteDir + "/" + zipFileName; // 使用实际上传路径
    
    for (LoanFile idCardFile : idCardFiles) {
        HXBKMaterial material = new HXBKMaterial();
        material.setMType("2"); // 图片类型
        material.setBigCode("20"); // 身份证图片
        material.setMeterialName(zipFileName); // 实际文件名
        material.setFilePath(filePath); // 完整路径
        
        if (idCardFile.getFileType() == FileType.ID_HEAD) {
            material.setSmallCode("201"); // 身份证人脸面
        } else if (idCardFile.getFileType() == FileType.ID_NATION) {
            material.setSmallCode("202"); // 身份证国徽面
        }
        
        materials.add(material);
    }
    
    return materials;
}
```

### 3. 新增uploadFaceImagesAndBuildMaterials方法

将原来的 `uploadFaceImages` 方法修改为 `uploadFaceImagesAndBuildMaterials`：

```java
private List<HXBKMaterial> uploadFaceImagesAndBuildMaterials(List<LoanFile> faceFiles, String applySerialNo) {
    // ... 原有上传逻辑 ...
    
    // 上传到SFTP
    String remoteDir = hxbkConfig.getPhotoDir() + "/" + currentDate;
    uploadToSftp(zipFilePath, remoteDir, zipFileName);
    uploadToSftp(indexFilePath, remoteDir, indexFileName);
    
    // 构建人脸资料信息
    List<HXBKMaterial> materials = new ArrayList<>();
    String filePath = remoteDir + "/" + zipFileName; // 使用实际上传路径
    
    for (LoanFile faceFile : faceFiles) {
        HXBKMaterial material = new HXBKMaterial();
        material.setMType("2"); // 图片类型
        material.setBigCode("26"); // 人脸图片
        material.setSmallCode("212"); // 活体人脸图片
        material.setMeterialName(zipFileName); // 实际文件名
        material.setFilePath(filePath); // 完整路径
        materials.add(material);
    }
    
    return materials;
}
```

## 📁 资料信息示例

### 修改前的问题：
- **meterialName**: "身份证人脸面" (描述性文字)
- **file_path**: "/download/contract/idcard/20250711/IDCARD-CJ20250711111111234.zip" (重新组装的路径)

### 修改后的正确格式：
- **meterialName**: "IDCARD-CJ20250711111111234.zip" (实际文件名)
- **file_path**: "/download_uat/contract/idcard/20250711/IDCARD-CJ20250711111111234.zip" (实际上传路径)

## 🔄 处理流程

```mermaid
sequenceDiagram
    participant CS as HXBKCreditService
    participant IFS as HXBKImageFileService
    participant SFTP as 蚂蚁SFTP

    CS->>IFS: uploadImageFilesToAntSftp()
    IFS->>IFS: uploadIdCardImagesAndBuildMaterials()
    IFS->>SFTP: 上传身份证ZIP文件
    IFS->>IFS: 构建身份证资料信息(使用实际文件名和路径)
    IFS->>IFS: uploadFaceImagesAndBuildMaterials()
    IFS->>SFTP: 上传人脸ZIP文件
    IFS->>IFS: 构建人脸资料信息(使用实际文件名和路径)
    IFS-->>CS: 返回完整的HXBKMaterial列表
    CS->>CS: buildCreditApplyRequest(materials)
```

## ✅ 验证要点

1. **文件名正确**：
   - 身份证：`IDCARD-{batchNo}.zip`
   - 人脸：`PHOTO-{batchNo}.zip`

2. **路径正确**：
   - 使用上传时的实际路径 `remoteDir + "/" + zipFileName`
   - 不重新组装路径

3. **资料类型映射**：
   - ID_HEAD → big_code: "20", small_code: "201"
   - ID_NATION → big_code: "20", small_code: "202"
   - ID_FACE → big_code: "26", small_code: "212"

## 🚀 部署说明

1. 确保修改后的代码能正常编译
2. 测试上传功能，验证资料信息的正确性
3. 检查授信申请中的materials字段是否包含正确的文件名和路径

## 📝 注意事项

1. **文件名一致性**：确保 `meterialName` 与实际上传的文件名完全一致
2. **路径准确性**：使用上传时生成的实际路径，避免路径不匹配
3. **批次号唯一性**：每次上传都会生成新的批次号，确保文件不会覆盖
4. **异常处理**：保持原有的异常处理逻辑不变
