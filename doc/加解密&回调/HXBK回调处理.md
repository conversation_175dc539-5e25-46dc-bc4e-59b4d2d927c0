# HXBK回调接口使用说明

## 概述

本模块实现了HXBK（湖消）回调接口，用于接收蚂蚁天枢系统的回调通知。

## 接口设计

### 统一回调接口

**接口地址**: `POST /hxbk/callback/notify`

**功能**: 接收蚂蚁侧的所有回调请求，根据请求中的`method`字段分发到不同的业务处理逻辑。

### 健康检查接口

**接口地址**: `GET /hxbk/callback/health`

**功能**: 检查回调服务的健康状态和加解密配置是否正常。

## 支持的回调方法

| Method | 描述 | 业务处理方法 |
|--------|------|-------------|
| `dubhe.credit.apply` | 授信申请回调 | `processCreditApplyCallback` |
| `dubhe.loan.apply` | 放款申请回调 | `processLoanApplyCallback` |
| `dubhe.repay.notify` | 还款通知回调 | `processRepayNotifyCallback` |
| `dubhe.credit.result` | 授信结果回调 | `processCreditResultCallback` |
| `dubhe.loan.result` | 放款结果回调 | `processLoanResultCallback` |

## 请求处理流程

1. **接收请求**: 接收蚂蚁侧的加密请求
2. **解密验签**: 使用`HXBKCryptoService.processIncomingRequest()`解密并验签
3. **方法分发**: 根据`method`字段分发到具体的业务处理方法
4. **业务处理**: 执行具体的业务逻辑
5. **构建响应**: 使用`HXBKCryptoService.buildSuccessResponse()`构建加密响应
6. **异常处理**: 捕获异常并返回相应的错误响应

## 示例请求

蚂蚁侧发送的请求格式（加密前）：

```json
{
  "method": "dubhe.credit.apply",
  "version": "1.0",
  "appid": "dubhe",
  "timestamp": "1695644916552",
  "signType": "SHA256WithRSA",
  "sign": "pa+lCXlakC1EH8Y4PoAG4XEOrvslWZAvbE0qm5XnPq0t...",
  "encrypt": "1",
  "secretKey": "QPGX8RECojNofyx1T9npbceApB1IA3Br2PrnEfxr7bR9...",
  "data": "yvq7LruMZFijTT02fxPH96Xz9Gi1S11xsdJ9CkcjSVjd...",
  "requestId": "dubhe1695644916552"
}
```

## 示例响应

成功响应格式（加密前）：

```json
{
  "code": "000000",
  "msg": "请求成功",
  "data": {
    "result": "processed",
    "message": "授信结果回调处理成功",
    "applySerialNo": "202507080001",
    "timestamp": 1720454400000
  },
  "sign": "...",
  "encrypt": "1",
  "secretKey": "...",
  "responseId": "..."
}
```

失败响应格式（不加密）：

```json
{
  "code": "500000",
  "msg": "系统异常",
  "responseId": "..."
}
```

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 000000 | 成功 |
| 100000 | 验签失败 |
| 200000 | 解密失败 |
| 400000 | 不支持的方法 |
| 500000 | 系统异常 |

## 扩展新的回调方法

要添加新的回调方法，需要：

1. 在`HXBKCallbackService.processCallback()`方法的switch语句中添加新的case
2. 实现对应的业务处理方法
3. 如果需要，创建对应的业务数据DTO类

示例：

```java
case "dubhe.new.method" -> processNewMethodCallback(requestBody);

private Object processNewMethodCallback(String requestBody) {
    // 实现具体的业务逻辑
    NewMethodRequest request = cryptoService.processIncomingRequest(
        requestBody, NewMethodRequest.class);
    
    // 处理业务逻辑...
    
    // 构建响应
    Map<String, Object> responseData = new HashMap<>();
    responseData.put("result", "success");
    return responseData;
}
```

## 配置要求

确保以下配置正确：

1. **密钥配置**: 在`application.yml`中配置HXBK相关的密钥
2. **网络配置**: 确保蚂蚁侧能够访问回调接口
3. **日志配置**: 配置适当的日志级别以便调试

## 测试

### 1. 健康检查

可以使用健康检查接口验证服务状态：

```bash
curl -X GET http://localhost:8080/hxbk/callback/health
```

预期响应：

```json
{
  "status": "UP",
  "timestamp": 1720454400000,
  "cryptoConfigValid": true
}
```

### 2. 回调接口测试

使用`CallbackTestUtil`工具类生成测试请求：

```java
// 生成授信结果回调测试请求
String testRequest = CallbackTestUtil.generateCreditResultCallbackRequest(
    antPublicKey, partnerPrivateKey);

// 发送测试请求
curl -X POST http://localhost:8080/hxbk/callback/notify \
  -H "Content-Type: application/json" \
  -d "$testRequest"
```

### 3. 测试用例

参考`CallbackTestUtil.main()`方法中的示例，可以生成各种类型的回调测试请求。

## 注意事项

1. **安全性**: 所有回调请求都经过加密和数字签名验证
2. **幂等性**: 回调处理应该具备幂等性，避免重复处理
3. **性能**: 回调接口应该快速响应，避免超时
4. **日志**: 记录关键信息但不记录敏感数据
5. **异常处理**: 妥善处理所有异常，不暴露系统内部信息
