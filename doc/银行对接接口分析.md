# 🏦 银行对接接口分析

## 📋 概述

本文档分析了JH-Loan-Cash-Capital项目中的银行对接接口，主要包括HXBK(湖消银行)和CYBK两家银行的对接情况。

## 🔗 HXBK (湖消银行) 接口分析

### 🔧 基础配置
```properties
# 接口基础URL
hxbk.server.baseUrl= https://apdevcenter.antchain.antgroup.com/developer/product/RISKPLUS?api=riskplus.dubbridge.usecredit.apply_1.0

# 服务时间窗口
hxbk.loan.start=2300
hxbk.loan.end=0300

# 认证信息
hxbk.server.ak=www.baidu.com
hxbk.server.sk=www.baidu.com
```

### 📁 文件路径配置
```properties
# 还款文件路径前缀
hxbk.returnfile.path.prefix=/download_uat/v2/repay/

# SFTP服务器配置
mayi.sftp.host = sftp-dev.alipay.com
mayi.sftp.username = loanplat
mayi.sftp.password = 123456
mayi.sftp.port = 22

# 放款文件路径
mayi.sftp.download.loan = /download_uat/v2/loan/

# 还款文件路径
mayi.sftp.download.repay = /download_uat/v2/repay/

# 合同文件路径
mayi.sftp.download.contract = /download_uat/contract/

# 身份证文件路径
mayi.sftp.download.idcard = /download/contract/idcard/

# 照片文件路径
mayi.sftp.download.photo = /download/contract/photo/
```

### 🔌 核心业务接口

#### 1. 授信申请接口
- **API**: `riskplus.dubbridge.usecredit.apply_1.0`
- **功能**: 向湖消银行发起授信申请
- **实现类**: `HXBKCreditService`

#### 2. 授信查询接口
- **API**: `riskplus.dubbridge.usecredit.query_1.0`
- **功能**: 查询授信申请结果
- **实现类**: `HXBKCreditService`

#### 3. 放款申请接口
- **API**: `riskplus.dubbridge.loan.apply_1.0`
- **功能**: 向湖消银行发起放款申请
- **实现类**: `HXBKLoanService`

#### 4. 放款查询接口
- **API**: `riskplus.dubbridge.loan.query_1.0`
- **功能**: 查询放款申请结果
- **实现类**: `HXBKLoanService`

#### 5. 还款申请接口
- **API**: `riskplus.dubbridge.repay.apply_1.0`
- **功能**: 向湖消银行发起还款申请
- **实现类**: `HXBKRepayService`

#### 6. 还款试算接口
- **API**: `riskplus.dubbridge.repay.trial_1.0`
- **功能**: 计算还款金额
- **实现类**: `HXBKRepayService`

#### 7. 批量还款接口
- **API**: `riskplus.dubbridge.repay.batch.apply_1.0`
- **功能**: 批量发起还款申请
- **实现类**: `HXBKRepayService`

#### 8. 结算证明查询接口
- **API**: `riskplus.dubbridge.settlement.certificate.query_1.0`
- **功能**: 查询结算证明
- **实现类**: `HXBKReccService`

### 📦 数据结构

#### 图片材料结构
```
m_type (0-风控报告,1-合同,2-图片,3-附件)
big_code (00-风控报告,10-合同,20-身份证图片,26-人脸图片,30-附件)
small_code (201-身份证人脸面,202-身份证国徽面,212-活体人脸图片)
```

#### 批次号格式
```
"CJ" + "年月日时分秒" + 四位随机数字 (如 CJ202507111111111234)
```

## 🔗 CYBK 接口分析

### 🔧 基础配置
CYBK的配置信息未在当前配置文件中明确列出，可能存储在Apollo配置中心。

### 🔌 核心业务接口

#### 1. 授信相关接口
- **实现类**: `CYBKCreditService`
- **功能**: 处理CYBK银行的授信申请和查询

#### 2. 放款相关接口
- **实现类**: `CYBKLoanService`
- **功能**: 处理CYBK银行的放款申请和查询

#### 3. 还款相关接口
- **实现类**: `CYBKRepayService`
- **功能**: 处理CYBK银行的还款申请和查询

#### 4. 对账相关接口
- **实现类**: `CYBKReccService`
- **功能**: 处理CYBK银行的对账文件

#### 5. 债权标记接口
- **任务类**: `CYBKClaimMarkJob`
- **功能**: 处理CYBK银行的债权标记

### 📦 数据结构

#### 对账文件格式
```
core放款ID|业务系统放款ID|期数|应还时间|实还时间|本金|利息|罚息|违约金|还款目的
```

## 🔄 通用接口设计模式

### 🏭 工厂模式
系统使用工厂模式根据银行渠道动态选择对应的服务实现：

```java
public ReccResultVo processRecc(ReccApplyVo applyVo) {
    BankChannel channel = applyVo.getChannel();
    getBankReccService(channel).process(applyVo);
    ReccResultVo result = new ReccResultVo();
    result.setActReccTime(LocalDateTime.now());
    result.setStatus(ProcessStatus.SUCCESS);
    return result;
}

public ReccResultVo query(ReccApplyVo applyVo) {
    BankChannel channel = applyVo.getChannel();
    return getBankReccService(channel).query(applyVo);
}
```

### 🔄 转换器模式
系统使用MapStruct进行DTO和VO之间的转换：

```java
@Override
public RestResult<BatchTrialResultDto> batchTrial(BatchTrailDto trailDto) {
    logger.info("core batch repay trial info: {}", JsonUtil.toJsonString(trailDto));

    BatchTrailVo trailVo = ApiRepayConvert.INSTANCE.toVo(trailDto);
    BatchTrialResultVo trailResultVos = manageService.batchTrial(trailVo);
    BatchTrialResultDto trailResultDto = ApiRepayConvert.INSTANCE.toDto(trailResultVos);

    logger.info("core batch repay trial info result: {}", JsonUtil.toJsonString(trailResultDto));
    return RestResult.success(trailResultDto);
}
```

## 🔍 接口调用流程

### 授信流程
1. 接收授信申请请求
2. 根据银行渠道选择对应的授信服务
3. 调用银行授信接口
4. 异步查询授信结果
5. 更新授信状态

### 放款流程
1. 接收放款申请请求
2. 根据银行渠道选择对应的放款服务
3. 调用银行放款接口
4. 异步查询放款结果
5. 更新放款状态

### 还款流程
1. 接收还款申请请求
2. 调用还款试算接口计算还款金额
3. 保存还款请求数据和明细
4. 调用银行还款接口
5. 更新还款状态

## 📊 批处理任务

### 对账任务
- **周期**: 每日凌晨执行
- **功能**: 生成对账文件并上传到SFTP服务器
- **实现**: 使用XXL-Job调度框架

### 债权标记任务
- **周期**: 每日执行
- **功能**: 处理前一天的债权标记
- **实现**: `CYBKClaimMarkJob`

## 🔒 安全措施

### 认证机制
- 使用AK/SK进行接口认证
- SFTP服务器使用用户名密码认证

### 数据加密
- 使用BouncyCastle加密库进行数据加密
- 敏感信息在传输和存储时进行加密

## 💡 接口优化建议

1. **统一接口规范**: 统一不同银行的接口调用方式
2. **增强错误处理**: 完善接口调用的错误处理机制
3. **增加接口监控**: 对关键接口增加性能和可用性监控
4. **优化重试机制**: 完善接口调用失败的重试策略
5. **完善日志记录**: 增强接口调用的日志记录，便于问题排查
