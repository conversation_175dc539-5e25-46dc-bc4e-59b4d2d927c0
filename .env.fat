# 内部环境标识
ENV = 'fat'
# NODE环境标识
NODE_ENV = 'development'

# 如果使用 Nginx 代理后端接口，那么此处需要改为 '/'，文件查看 Docker 部署篇，Nginx 配置
# 接口地址，注意协议，如果你没有配置 ssl，需要将 https 改为 http
VUE_APP_BASE_API  = 'http://47.117.21.34:8001/admin/api'
# 如果接口是 http 形式， wss 需要改为 ws
VUE_APP_WS_API = 'ws://47.117.21.34:8001/admin/ws'

# 是否需要sourcemap 生产需要关闭
VUE_NEED_SOURCE_MAP = true
# 是否启用 babel-plugin-dynamic-import-node插件
VUE_CLI_BABEL_TRANSPILE_MODULES = true
