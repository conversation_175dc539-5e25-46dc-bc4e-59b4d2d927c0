package com.jinghang.cash.api.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/22 17:04
 */
public enum LoanStage {
    RISK("风控前"),
    CREDIT("授信"),
    LOAN("放款"),
    LOAN_AFTER("放款后"),
    REPAY("还款"),
    RECONCILIATION("对账"),
    REFUND("退款");
    private final String desc;

    LoanStage(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
