package com.maguo.loan.cash.flow.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记一个方法作为借款申请的入口，用于AOP记录请求。
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogLoanRequest {
    /**
     * 流量标识，用于区分不同的业务或渠道来源。
     */
    String flowIdentifier();
}
