package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @since 2024-10-16
 */
public enum AppSingleHomeCardRouter {

    //申请借款
    APPLY("最高可借额度(元)", "申请借钱", "即刻获取额度", null, null),
    //审核未通过
    AUDIT_REJECT(null, null, null, "很抱歉！审核未通过", "请于申请日30天后重新尝试借款"),
    //审核中
    AUDITING("最高可借额度(元)", "审核中", null, null, null),
    //去提现
    WITHDRAW("可用借款额度(元)", "去提现", "快速到账", null, null),
    //审核中 未购买权益
    NOT_BUY_RIGHTS("可用借款额度(元)", "审核中", "快速到账", null, null),
    //放款中
    LOANING("已用借款额度(元)", "放款中", "快速到账", null, null),
    //再借一笔
    APPLY_AGAIN("最高可借额度(元)", "再借一笔", "额度更高 下款更快", null, null),
    //去还款
    TO_REPAY("已用借款额度(元)", "去还款", null, null, null);

    /**
     * 标题
     */
    private final String title;
    /**
     * 按钮文案
     */
    private final String buttonText;
    /**
     * 按钮浮动文案
     */
    private final String buttonFloatText;

    private final String rejectTitle;

    private final String rejectSubtitle;

    AppSingleHomeCardRouter(String title, String buttonText, String buttonFloatText, String rejectTitle, String rejectSubtitle) {
        this.buttonText = buttonText;
        this.buttonFloatText = buttonFloatText;
        this.title = title;
        this.rejectTitle = rejectTitle;
        this.rejectSubtitle = rejectSubtitle;
    }

    public String getButtonText() {
        return buttonText;
    }

    public String getButtonFloatText() {
        return buttonFloatText;
    }

    public String getTitle() {
        return title;
    }

    public String getRejectTitle() {
        return rejectTitle;
    }

    public String getRejectSubtitle() {
        return rejectSubtitle;
    }
}
