package com.maguo.loan.cash.flow.enums;

/**
 * 职业（国标）
 */
public enum Occupation {
    ZERO("0", "国家机关、党群组织、企业、事业单位负责人"),
    ONE("1", "专业技术人员"),
    THREE("3", "办事人员和有关人员"),
    FOUR("4", "商业、服务业人员"),
    FIVE("5", "农、林、牧、渔、水利业生产人员"),
    SIX("6", "生产、运输设备操作人员及有关人员"),
    X("X", "军人"),
    Y("Y", "不便分类的其他从业人员"),
    Z("Z", "未知");
    private String code;
    private String desc;
    Occupation(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getOccupation(Position position) {
        switch (position) {
            case TWENTY_TWO:
                return X.code;
            case TWENTY_ONE, TWENTY:
                return ONE.code;
            case NINETEEN:
                return THREE.code;
            case EIGHTEEN, SEVENTEEN, SIXTEEN, FIFTEEN:
                return FOUR.code;
            case FOURTEEN:
                return Y.code;
            case THIRTEEN:
                return SIX.code;
            default:
                return ZERO.code;
        }
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
}
