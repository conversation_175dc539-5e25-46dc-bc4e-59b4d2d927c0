package com.maguo.loan.cash.flow.entrance.bairong.dto.loan;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

public class BairongCreditApplyRequest {

    private LoanInfo loanInfo;

    // 申请人信息
    private BasicInfo basicInfo;

    // 账户信息列表
    private List<AccInfo> accInfoList;

    private AccInfo accountInfo;

    // 职业信息
    private OccupationInfo occupationInfo;

    // 家庭信息
    private FamilyInfo familyInfo;

    // 联系人信息列表
    private List<RelationInfo> relationList;

    // 影像信息列表
    private List<ImageInfo> imageInfoList;

    // 扩展信息
    private ExtendInfo extendInfo;


    private String channel;

    private String projectCode;


    // ----- 以下是内部类定义 -----

    // 贷款信息
    @Data
    public static class LoanInfo {
        private String outLoanSeq;
        private String applCde;
        private String applyDt;
        private String loanTyp;
        private BigDecimal dnAmt;
        private Integer applyTnr; // 注意：文档中是Number(16,0)
        private String mtdCde;
        private String purpose;
        private String otherPurpose;
        private BigDecimal priceIntRat;
        private BigDecimal custRate;
        private String dueDayOpt;
        private String dueDay;
        private String directFlag;

        public String getOutLoanSeq() {
            return outLoanSeq;
        }

        public void setOutLoanSeq(String outLoanSeq) {
            this.outLoanSeq = outLoanSeq;
        }

        public String getApplCde() {
            return applCde;
        }

        public void setApplCde(String applCde) {
            this.applCde = applCde;
        }

        public String getApplyDt() {
            return applyDt;
        }

        public void setApplyDt(String applyDt) {
            this.applyDt = applyDt;
        }

        public String getLoanTyp() {
            return loanTyp;
        }

        public void setLoanTyp(String loanTyp) {
            this.loanTyp = loanTyp;
        }

        public BigDecimal getDnAmt() {
            return dnAmt;
        }

        public void setDnAmt(BigDecimal dnAmt) {
            this.dnAmt = dnAmt;
        }

        public Integer getApplyTnr() {
            return applyTnr;
        }

        public void setApplyTnr(Integer applyTnr) {
            this.applyTnr = applyTnr;
        }

        public String getMtdCde() {
            return mtdCde;
        }

        public void setMtdCde(String mtdCde) {
            this.mtdCde = mtdCde;
        }

        public String getPurpose() {
            return purpose;
        }

        public void setPurpose(String purpose) {
            this.purpose = purpose;
        }

        public String getOtherPurpose() {
            return otherPurpose;
        }

        public void setOtherPurpose(String otherPurpose) {
            this.otherPurpose = otherPurpose;
        }

        public BigDecimal getPriceIntRat() {
            return priceIntRat;
        }

        public void setPriceIntRat(BigDecimal priceIntRat) {
            this.priceIntRat = priceIntRat;
        }

        public BigDecimal getCustRate() {
            return custRate;
        }

        public void setCustRate(BigDecimal custRate) {
            this.custRate = custRate;
        }

        public String getDueDayOpt() {
            return dueDayOpt;
        }

        public void setDueDayOpt(String dueDayOpt) {
            this.dueDayOpt = dueDayOpt;
        }

        public String getDueDay() {
            return dueDay;
        }

        public void setDueDay(String dueDay) {
            this.dueDay = dueDay;
        }

        public String getDirectFlag() {
            return directFlag;
        }

        public void setDirectFlag(String directFlag) {
            this.directFlag = directFlag;
        }
    }

    public static class BasicInfo {
        private String apptTyp;
        private String custName;
        private String idTyp;
        private String idTypOth;
        private String idNo;
        private String idNoStartDate;
        private String idNoEndDate;
        private String idOrgan;
        private String bornDate;
        private String indivMobile;
        private String indivSex;
        private Integer apptAge;
        private String indivMarital;
        private String indivEdu;
        private String indivDegree;
        private String idCardAddress;
        private String nation;

        public String getApptTyp() {
            return apptTyp;
        }

        public void setApptTyp(String apptTyp) {
            this.apptTyp = apptTyp;
        }

        public String getCustName() {
            return custName;
        }

        public void setCustName(String custName) {
            this.custName = custName;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getIdTypOth() {
            return idTypOth;
        }

        public void setIdTypOth(String idTypOth) {
            this.idTypOth = idTypOth;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getIdNoStartDate() {
            return idNoStartDate;
        }

        public void setIdNoStartDate(String idNoStartDate) {
            this.idNoStartDate = idNoStartDate;
        }

        public String getIdNoEndDate() {
            return idNoEndDate;
        }

        public void setIdNoEndDate(String idNoEndDate) {
            this.idNoEndDate = idNoEndDate;
        }

        public String getIdOrgan() {
            return idOrgan;
        }

        public void setIdOrgan(String idOrgan) {
            this.idOrgan = idOrgan;
        }

        public String getBornDate() {
            return bornDate;
        }

        public void setBornDate(String bornDate) {
            this.bornDate = bornDate;
        }

        public String getIndivMobile() {
            return indivMobile;
        }

        public void setIndivMobile(String indivMobile) {
            this.indivMobile = indivMobile;
        }

        public String getIndivSex() {
            return indivSex;
        }

        public void setIndivSex(String indivSex) {
            this.indivSex = indivSex;
        }

        public Integer getApptAge() {
            return apptAge;
        }

        public void setApptAge(Integer apptAge) {
            this.apptAge = apptAge;
        }

        public String getIndivMarital() {
            return indivMarital;
        }

        public void setIndivMarital(String indivMarital) {
            this.indivMarital = indivMarital;
        }

        public String getIndivEdu() {
            return indivEdu;
        }

        public void setIndivEdu(String indivEdu) {
            this.indivEdu = indivEdu;
        }

        public String getIndivDegree() {
            return indivDegree;
        }

        public void setIndivDegree(String indivDegree) {
            this.indivDegree = indivDegree;
        }

        public String getIdCardAddress() {
            return idCardAddress;
        }

        public void setIdCardAddress(String idCardAddress) {
            this.idCardAddress = idCardAddress;
        }

        public String getNation() {
            return nation;
        }

        public void setNation(String nation) {
            this.nation = nation;
        }
    }

    // 账号信息
    public static class AccInfo {
        private String acctKind;
        private String acctBankCode;
        private String acctNo;
        private String acctName;
        private String idTyp;
        private String idNo;
        private String acctPhone;
        private String payChannel;
        private String agreeNum;

        public String getAcctKind() {
            return acctKind;
        }

        public void setAcctKind(String acctKind) {
            this.acctKind = acctKind;
        }

        public String getAcctBankCode() {
            return acctBankCode;
        }

        public void setAcctBankCode(String acctBankCode) {
            this.acctBankCode = acctBankCode;
        }

        public String getAcctNo() {
            return acctNo;
        }

        public void setAcctNo(String acctNo) {
            this.acctNo = acctNo;
        }

        public String getAcctName() {
            return acctName;
        }

        public void setAcctName(String acctName) {
            this.acctName = acctName;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getAcctPhone() {
            return acctPhone;
        }

        public void setAcctPhone(String acctPhone) {
            this.acctPhone = acctPhone;
        }

        public String getPayChannel() {
            return payChannel;
        }

        public void setPayChannel(String payChannel) {
            this.payChannel = payChannel;
        }

        public String getAgreeNum() {
            return agreeNum;
        }

        public void setAgreeNum(String agreeNum) {
            this.agreeNum = agreeNum;
        }
    }

    // 职业信息

    public static class OccupationInfo {
        private String profession;
        private String indivPosition;
        private String indivProfsn;
        private String belongsIndus;
        private String indivEmpName;
        private Address indivEmpAddInfo;
        private String monthlyIncome;


        public static class Address {
            private String province;
            private String city;
            private String country;
            private String detailAddress;

            public String getProvince() {
                return province;
            }


            public void setProvince(String province) {
                this.province = province;
            }

            public String getCity() {
                return city;
            }

            public void setCity(String city) {
                this.city = city;
            }

            public String getCountry() {
                return country;
            }

            public void setCountry(String country) {
                this.country = country;
            }

            public String getDetailAddress() {
                return detailAddress;
            }

            public void setDetailAddress(String detailAddress) {
                this.detailAddress = detailAddress;
            }
        }

        public String getProfession() {
            return profession;
        }

        public void setProfession(String profession) {
            this.profession = profession;
        }

        public String getIndivPosition() {
            return indivPosition;
        }

        public void setIndivPosition(String indivPosition) {
            this.indivPosition = indivPosition;
        }

        public String getIndivProfsn() {
            return indivProfsn;
        }

        public void setIndivProfsn(String indivProfsn) {
            this.indivProfsn = indivProfsn;
        }

        public String getBelongsIndus() {
            return belongsIndus;
        }

        public void setBelongsIndus(String belongsIndus) {
            this.belongsIndus = belongsIndus;
        }

        public String getIndivEmpName() {
            return indivEmpName;
        }

        public void setIndivEmpName(String indivEmpName) {
            this.indivEmpName = indivEmpName;
        }

        public Address getIndivEmpAddInfo() {
            return indivEmpAddInfo;
        }

        public void setIndivEmpAddInfo(Address indivEmpAddInfo) {
            this.indivEmpAddInfo = indivEmpAddInfo;
        }

        public String getMonthlyIncome() {
            return monthlyIncome;
        }

        public void setMonthlyIncome(String monthlyIncome) {
            this.monthlyIncome = monthlyIncome;
        }
    }

    // 家庭信息

    public static class FamilyInfo {
        private Address liveAddInfo;
        private String liveZip;
        private String liveInfo;
        private String indivFmlyTel;
        private Address regAddInfo;
        private Address commAddInfo;
        private String commZip;


        public static class Address {
            private String province;
            private String city;
            private String country;
            private String detailAddress;

            public String getProvince() {
                return province;
            }

            public void setProvince(String province) {
                this.province = province;
            }

            public String getCity() {
                return city;
            }

            public void setCity(String city) {
                this.city = city;
            }

            public String getCountry() {
                return country;
            }

            public void setCountry(String country) {
                this.country = country;
            }

            public String getDetailAddress() {
                return detailAddress;
            }

            public void setDetailAddress(String detailAddress) {
                this.detailAddress = detailAddress;
            }
        }

        public Address getLiveAddInfo() {
            return liveAddInfo;
        }

        public void setLiveAddInfo(Address liveAddInfo) {
            this.liveAddInfo = liveAddInfo;
        }

        public String getLiveZip() {
            return liveZip;
        }

        public void setLiveZip(String liveZip) {
            this.liveZip = liveZip;
        }

        public String getLiveInfo() {
            return liveInfo;
        }

        public void setLiveInfo(String liveInfo) {
            this.liveInfo = liveInfo;
        }

        public String getIndivFmlyTel() {
            return indivFmlyTel;
        }

        public void setIndivFmlyTel(String indivFmlyTel) {
            this.indivFmlyTel = indivFmlyTel;
        }

        public Address getRegAddInfo() {
            return regAddInfo;
        }

        public void setRegAddInfo(Address regAddInfo) {
            this.regAddInfo = regAddInfo;
        }

        public Address getCommAddInfo() {
            return commAddInfo;
        }

        public void setCommAddInfo(Address commAddInfo) {
            this.commAddInfo = commAddInfo;
        }

        public String getCommZip() {
            return commZip;
        }

        public void setCommZip(String commZip) {
            this.commZip = commZip;
        }
    }

    // 联系人信息

    public static class RelationInfo {
        private String relName;
        private String relMobile;
        private String relRelation;

        public String getRelName() {
            return relName;
        }

        public void setRelName(String relName) {
            this.relName = relName;
        }

        public String getRelMobile() {
            return relMobile;
        }

        public void setRelMobile(String relMobile) {
            this.relMobile = relMobile;
        }

        public String getRelRelation() {
            return relRelation;
        }

        public void setRelRelation(String relRelation) {
            this.relRelation = relRelation;
        }
    }

    // 影像信息

    public static class ImageInfo {
        private String imageName;
        private String imageType;
        private String imageUrl;

        public String getImageName() {
            return imageName;
        }

        public void setImageName(String imageName) {
            this.imageName = imageName;
        }

        public String getImageType() {
            return imageType;
        }

        public void setImageType(String imageType) {
            this.imageType = imageType;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }
    }

    // 扩展信息

    public static class ExtendInfo {
        private String custTransScore1;
        private String custTransScore2;
        private String custTransScore3;
        private String custTransScore4;
        private String custTransScore5;
        private String teSource;

        public String getCustTransScore1() {
            return custTransScore1;
        }

        public void setCustTransScore1(String custTransScore1) {
            this.custTransScore1 = custTransScore1;
        }

        public String getCustTransScore2() {
            return custTransScore2;
        }

        public void setCustTransScore2(String custTransScore2) {
            this.custTransScore2 = custTransScore2;
        }

        public String getCustTransScore3() {
            return custTransScore3;
        }

        public void setCustTransScore3(String custTransScore3) {
            this.custTransScore3 = custTransScore3;
        }

        public String getCustTransScore4() {
            return custTransScore4;
        }

        public void setCustTransScore4(String custTransScore4) {
            this.custTransScore4 = custTransScore4;
        }

        public String getCustTransScore5() {
            return custTransScore5;
        }

        public void setCustTransScore5(String custTransScore5) {
            this.custTransScore5 = custTransScore5;
        }

        public String getTeSource() {
            return teSource;
        }

        public void setTeSource(String teSource) {
            this.teSource = teSource;
        }
    }

    public LoanInfo getLoanInfo() {
        return loanInfo;
    }

    public void setLoanInfo(LoanInfo loanInfo) {
        this.loanInfo = loanInfo;
    }

    public BasicInfo getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(BasicInfo basicInfo) {
        this.basicInfo = basicInfo;
    }

    public List<AccInfo> getAccInfoList() {
        return accInfoList;
    }

    public void setAccInfoList(List<AccInfo> accInfoList) {
        this.accInfoList = accInfoList;
    }

    public OccupationInfo getOccupationInfo() {
        return occupationInfo;
    }

    public void setOccupationInfo(OccupationInfo occupationInfo) {
        this.occupationInfo = occupationInfo;
    }

    public FamilyInfo getFamilyInfo() {
        return familyInfo;
    }

    public void setFamilyInfo(FamilyInfo familyInfo) {
        this.familyInfo = familyInfo;
    }

    public List<RelationInfo> getRelationList() {
        return relationList;
    }

    public void setRelationList(List<RelationInfo> relationList) {
        this.relationList = relationList;
    }

    public List<ImageInfo> getImageInfoList() {
        return imageInfoList;
    }

    public void setImageInfoList(List<ImageInfo> imageInfoList) {
        this.imageInfoList = imageInfoList;
    }

    public ExtendInfo getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(ExtendInfo extendInfo) {
        this.extendInfo = extendInfo;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public AccInfo getAccountInfo() {
        return accountInfo;
    }

    public void setAccountInfo(AccInfo accountInfo) {
        this.accountInfo = accountInfo;
    }


}
