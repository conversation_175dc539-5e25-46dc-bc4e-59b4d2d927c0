package com.maguo.loan.cash.flow.service.event.listener;


import com.alibaba.fastjson2.JSONObject;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.common.loan.PlanGenerator;
import com.jinghang.common.loan.plan.InterestType;
import com.jinghang.common.loan.plan.RepayPlan;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.config.RiskConfig;
import com.maguo.loan.cash.flow.entity.ApprovalRecord;
import com.maguo.loan.cash.flow.entity.BaiRongApplyAccountInfo;
import com.maguo.loan.cash.flow.entity.BaiRongApplyBasicInfo;
import com.maguo.loan.cash.flow.entity.BaiRongLoanApply;
import com.maguo.loan.cash.flow.entity.LvxinApplyRecord;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserRevolving;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongLoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongService;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.entrance.lvxin.convert.LvxinBaseConvert;
import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.ApplyType;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.enums.RevolvingState;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.enums.RiskChannel;
import com.maguo.loan.cash.flow.enums.SmsTemplate;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.nfsp.req.SmsSendReq;
import com.maguo.loan.cash.flow.repository.ApprovalRecordRepository;
import com.maguo.loan.cash.flow.repository.FlowConfigRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.LvxinApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderBindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserRevolvingRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongApplyAccountInfoRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongApplyBasicInfoRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongLoanApplyRepository;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderCardService;
import com.maguo.loan.cash.flow.service.RiskService;
import com.maguo.loan.cash.flow.service.SmsService;
import com.maguo.loan.cash.flow.service.UserRevolvingService;
import com.maguo.loan.cash.flow.service.UserService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.maguo.loan.cash.flow.service.event.RiskResultEvent;
import com.maguo.loan.cash.flow.service.event.RiskResultOutEvent;
import com.maguo.loan.cash.flow.util.BaseConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 风控结果事件
 *
 * <AUTHOR>
 */
@Component
public class RiskResultEventListener {

    private static final Logger logger = LoggerFactory.getLogger(RiskResultEventListener.class);

    @Autowired
    private WarningService warningService;

    @Autowired
    private MqService mqService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private AgreementService agreementService;

    @Autowired
    private LvxinApplyRecordRepository lvxinApplyRecordRepository;

    @Autowired
    private ApprovalRecordRepository approvalRecordRepository;

    @Autowired
    private SmsService smsService;


    @Autowired
    private UserRevolvingRepository userRevolvingRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;


    @Autowired
    private OrderBindCardRecordRepository orderBindCardRecordRepository;

    @Autowired
    private FlowConfigRepository flowConfigRepository;

    @Autowired
    private OrderCardService orderCardService;

    @Autowired
    private UserRevolvingService userRevolvingService;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private LockService lockService;

    @Autowired
    private RiskService riskService;
    @Autowired
    private RiskConfig riskConfig;

    @Autowired
    private BairongService bairongService;

    @Autowired
    private BairongLoanApplyRepository bairongLoanApplyRepository;

    @Autowired
    private BairongApplyBasicInfoRepository bairongApplyBasicInfoRepository;

    @Autowired
    private BairongApplyAccountInfoRepository bairongApplyAccountInfoRepository;

    public static final int LOCK_WAIT_SECOND = 3;

    public static final int LOCK_RELEASE_SECOND = 8;

    @Autowired
    private LoanRepository loanRepository;

    private static final int AMOUNT_TYPE_EXPIRE_MONTH = 3;
    private static final int AMOUNT_EXPIRE_MONTH = 1;

    @EventListener(RiskResultEvent.class)
    public void onApplicationEvent(RiskResultEvent riskResultEvent) {
        logger.info("风控结果事件：{}", riskResultEvent);
        String riskId = riskResultEvent.getRiskId();
        UserRiskRecord record = userRiskRecordRepository.findById(riskId).orElseThrow();
        FlowChannel flowChannel = record.getFlowChannel();
        if (record.getApplyType() == ApplyType.RISK_REVOLVING) {
            binningHandler(record);
        } else {
            if (record.getAmountType() == AmountType.REVOLVING) {
                binningHandler(record);
            }
            riskHandler(record, flowChannel, riskId);
        }
    }

    @EventListener(RiskResultOutEvent.class)
    public void onApplicationEvent(RiskResultOutEvent riskResultOutEvent) {
        logger.info("外部风控结果事件：{}", JsonUtil.toJsonString(riskResultOutEvent));
        String riskId = riskResultOutEvent.getRiskId();
        UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);

        riskHandler(record, riskId);
    }

    private void binningHandler(UserRiskRecord record) {
        logger.info("风控结果事件-循环额度分箱操作：{}", record);
        Optional<UserRevolving> revolvingOptional = userRevolvingRepository.findById(record.getUserId());
        AmountType amountType = record.getAmountType();
        if (amountType != AmountType.REVOLVING
                || (record.getApproveResult() == AuditState.REJECT && record.getApplyType() == ApplyType.RISK_REVOLVING)) {
            if (revolvingOptional.isPresent()) {
                UserRevolving revolving = revolvingOptional.get();
                if (revolving.getRevolvingState() != RevolvingState.INVALID) {
                    revolving.setRevolvingState(RevolvingState.INVALID);
                    revolving.setRevolvingExpireTime(LocalDateTime.now().plusMonths(AMOUNT_TYPE_EXPIRE_MONTH));
                    revolving.setAmountExpireTime(LocalDateTime.now().plusMonths(AMOUNT_EXPIRE_MONTH));
                    userRevolvingRepository.save(revolving);
                }
            }
            return;
        }
        UserInfo userInfo = userInfoRepository.findById(record.getUserId()).orElseThrow(() -> new RuntimeException("当前用户不存在"));
        BigDecimal amount = record.getApproveAmount();
        // 循环额度
        boolean isFirst = true;
        UserRevolving userRevolving;
        if (revolvingOptional.isPresent()) {
            userRevolving = revolvingOptional.get();
            isFirst = false;
        } else {
            userRevolving = new UserRevolving();
            userRevolving.setApproveAmount(BigDecimal.ZERO);
            userRevolving.setId(userInfo.getId());
        }
        userRevolving.setAmountExpireTime(LocalDateTime.now().plusMonths(AMOUNT_EXPIRE_MONTH));
        userRevolving.setRevolvingExpireTime(LocalDateTime.now().plusMonths(AMOUNT_TYPE_EXPIRE_MONTH));
        userRevolving.setRevolvingState(RevolvingState.UPDATING);
        userRevolving = userRevolvingRepository.save(userRevolving);

        // 拆箱操作
        userRevolvingService.revolvingReleateHandler(amount, userRevolving, isFirst);
        userRevolving.setApproveAmount(amount);
        userRevolving.setRevolvingState(RevolvingState.NORMAL);
        userRevolvingRepository.save(userRevolving);
    }

    /**
     * 风控结果处理
     *
     * @param record      record
     * @param flowChannel flowChannel
     * @param riskId      riskId
     */
    private void riskHandler(UserRiskRecord record, FlowChannel flowChannel, String riskId) {
            // 风控结果
            AuditState approveResult = record.getApproveResult();
            if (AuditState.PASS == approveResult) {
                // 生成订单
                Order order = createOrder(record);
                // 更新预订单状态
                updatePreOrderPass(record);

                //TODO 在这里调用百融放款结果通知 加入百融的判断
                if (order.getFlowChannel().equals(FlowChannel.LTFQ)){
                    BairongLoanApplyRequest request = new BairongLoanApplyRequest();
                    Optional<BaiRongLoanApply> outLoanSeq = bairongLoanApplyRepository.findByOutLoanSeq(order.getOuterOrderId());
                    if (outLoanSeq.isPresent()){
                        Order orderOuter = orderRepository.findByOuterOrderId(order.getOuterOrderId());
                        BaiRongLoanApply loanApply = outLoanSeq.get();
                        String applyBasicInfoId = loanApply.getApplyBasicInfoId();
                        BaiRongApplyBasicInfo basicInfo = bairongApplyBasicInfoRepository.findById(applyBasicInfoId).get();
                        String accountInfoId = basicInfo.getAccountInfoId();
                        // Todo 一个申请人可能存在多个账号的情况
                        BaiRongApplyAccountInfo accountInfo = bairongApplyAccountInfoRepository.findById(accountInfoId).get();
                        BairongLoanApplyRequest.AccountInfo accInfo = new BairongLoanApplyRequest().new AccountInfo();
                        accInfo.setAcctKind(accountInfo.getAcctKind());
                        accInfo.setAcctBankCode(accountInfo.getAcctBankCode());
                        accInfo.setPayChannel(accountInfo.getPayChannel());
                        accInfo.setIdTyp(accountInfo.getIdTyp());
                        accInfo.setIdNo(accountInfo.getIdNo());
                        accInfo.setAcctPhone(accountInfo.getAcctPhone());
                        accInfo.setAcctName(accountInfo.getAcctName());
                        accInfo.setAcctNo(accountInfo.getAcctNo());
                        accInfo.setAgreeNum(accountInfo.getAgreeNum());
                        request.setAccountInfo(accInfo);
                        request.setUserId(orderOuter.getUserId());
                        request.setOutLoanSeq(loanApply.getOutLoanSeq());
                        request.setApplyTnr(loanApply.getApplyTnr());
                        request.setDnAmt(loanApply.getDnAmt());
                        request.setPurpose(loanApply.getPurpose());
                        request.setMtdCde(loanApply.getMtdCde());
                        bairongService.loanApply(request);
                    }
                }
            }
            if (AuditState.REJECT == approveResult) {
                updatePreOrderReject(record);
            }
    }


    /**
     * 风控结果处理
     *
     * @param record      record
     * @param riskId      riskId
     */
    private void riskHandler(UserRiskRecordExternal record, String riskId) {
        Locker lock = lockService.getLock("risk_result_handler_" + riskId);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.info("riskId: [{}], 风控结果处理未获取到锁", riskId);
                return;
            }

            PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElse(null);
            if (PreOrderState.AUDIT_PASS.name().equals(preOrder.getPreOrderState().name())
                || PreOrderState.AUDIT_REJECT.name().equals(preOrder.getPreOrderState().name())){
                //已经是终态，说明已经进行风控结果处理，直接返回
                return;
            }
            // 风控结果
            AuditState approveResult = record.getApproveResult();
            if (AuditState.PASS == approveResult) {
                // 生成订单
                Order order = createOrder(record);
                // 更新预订单状态
                updatePreOrderPass(record);
                if (riskConfig.getRiskEnable() && RiskChannel.BW.name().equals(record.getRiskChannel().name())){
                    //终态通知百维风控
                    mqService.submitRiskResultNoticeOut(record.getId());
                }

                //TODO 在这里调用百融放款结果通知 加入百融的判断
                if (order.getFlowChannel().equals(FlowChannel.LTFQ)){
                    BairongLoanApplyRequest request = new BairongLoanApplyRequest();
                    Optional<BaiRongLoanApply> outLoanSeq = bairongLoanApplyRepository.findByOutLoanSeq(order.getOuterOrderId());
                    if (outLoanSeq.isPresent()){
                        Order orderOuter = orderRepository.findByOuterOrderId(order.getOuterOrderId());
                        BaiRongLoanApply loanApply = outLoanSeq.get();
                        String applyBasicInfoId = loanApply.getApplyBasicInfoId();
                        BaiRongApplyBasicInfo basicInfo = bairongApplyBasicInfoRepository.findById(applyBasicInfoId).get();
                        String accountInfoId = basicInfo.getAccountInfoId();
                        // Todo 一个申请人可能存在多个账号的情况
                        BaiRongApplyAccountInfo accountInfo = bairongApplyAccountInfoRepository.findById(accountInfoId).get();
                        BairongLoanApplyRequest.AccountInfo accInfo = new BairongLoanApplyRequest().new AccountInfo();
                        accInfo.setAcctKind(accountInfo.getAcctKind());
                        accInfo.setAcctBankCode(accountInfo.getAcctBankCode());
                        accInfo.setPayChannel(accountInfo.getPayChannel());
                        accInfo.setIdTyp(accountInfo.getIdTyp());
                        accInfo.setIdNo(accountInfo.getIdNo());
                        accInfo.setAcctPhone(accountInfo.getAcctPhone());
                        accInfo.setAcctName(accountInfo.getAcctName());
                        accInfo.setAcctNo(accountInfo.getAcctNo());
                        accInfo.setAgreeNum(accountInfo.getAgreeNum());
                        request.setAccountInfo(accInfo);
                        request.setUserId(orderOuter.getUserId());
                        request.setOutLoanSeq(loanApply.getOutLoanSeq());
                        request.setApplyTnr(loanApply.getApplyTnr());
                        request.setDnAmt(loanApply.getDnAmt());
                        request.setPurpose(loanApply.getPurpose());
                        request.setMtdCde(loanApply.getMtdCde());
                        bairongService.loanApply(request);
                    }
                }
            }
            if (AuditState.REJECT == approveResult) {
                updatePreOrderReject(record);
                if (riskConfig.getRiskEnable() && RiskChannel.BW.name().equals(record.getRiskChannel().name())){
                    //终态通知百维风控
                    mqService.submitRiskResultNoticeOut(record.getId());
                }
            }
        }catch (Exception e){
            logger.error("风控结果处理异常",e);
        }finally {
            lock.unlock();
        }
    }

    private void sendSms(FlowChannel flowChannel, Order order) {
        Map<String, String> smsParam = Map.of("flowChannel", flowChannel.getAppName());
        SmsTemplate smsTemp = Objects.equals(ApplicationSource.MINI_WECHAT_QHYX, order.getApplicationSource())
                ? SmsTemplate.MINI_WECHAT_QHYX_AMOUNT_NOT_APPLIED
                : SmsTemplate.AMOUNT_NOT_APPLIED;
        // 暂时处理 等优享小程序模板搞好后移除判断
        if (smsTemp == SmsTemplate.AMOUNT_NOT_APPLIED) {
            //立即发送短信
            smsService.sendSms(smsTemp, smsParam, order.getMobile());
            //3小时后发送短信
            SmsSendReq smsSendReq = new SmsSendReq();
            smsSendReq.setType("2");
            smsSendReq.setTemplateId(smsTemp.getTemplateNo());
            smsSendReq.setPhoneList(List.of(order.getMobile()));
            smsSendReq.setMessageParamMap(smsParam);
            smsSendReq.setContext(order.getId());
            mqService.submitSmsSend3HDelay(JsonUtil.toJsonString(smsSendReq));
        }
    }

    /**
     * 更新预订单表状态为审批通过
     */
    private void updatePreOrderPass(UserRiskRecord record) {
        preOrderRepository.findByRiskId(record.getId()).ifPresent(p -> {
            p.setAmountType(record.getAmountType());
            p.setPreOrderState(PreOrderState.AUDIT_PASS);
            preOrderRepository.save(p);
        });
    }

    /**
     * 更新预订单表状态为审批通过
     */
    private void updatePreOrderPass(UserRiskRecordExternal record) {
        preOrderRepository.findByRiskId(record.getId()).ifPresent(p -> {
            p.setAmountType(record.getAmountType());
            p.setPreOrderState(PreOrderState.AUDIT_PASS);
            preOrderRepository.save(p);
        });
    }

    /**
     * 更新预订单表状态为审批拒绝
     */
    private void updatePreOrderReject(UserRiskRecord record) {
        preOrderRepository.findByRiskId(record.getId()).ifPresent(p -> {
            p.setPreOrderState(PreOrderState.AUDIT_REJECT);
            p.setIsReject(WhetherState.Y);
            p.setRemark(BaseConstants.DEFAULT_RISK_REJECT_REASON);
            preOrderRepository.save(p);
        });
    }

    /**
     * 更新预订单表状态为审批拒绝
     */
    private void updatePreOrderReject(UserRiskRecordExternal record) {
        preOrderRepository.findByRiskId(record.getId()).ifPresent(p -> {
            p.setPreOrderState(PreOrderState.AUDIT_REJECT);
            p.setIsReject(WhetherState.Y);
            p.setRemark(BaseConstants.DEFAULT_RISK_REJECT_REASON);
            preOrderRepository.save(p);
        });
    }

    private Order createOrder(UserRiskRecord record) {
        Order order = switch (record.getFlowChannel()) {
            default -> createNormalOrder(record);
        };

        //生成订单绑卡记录
        orderCardService.genOrderCardRecord(order);
        //agreementService.applyRegisterSign(order.getRiskId());
        return order;
    }

    private Order createOrder(UserRiskRecordExternal record) {
        Order order = switch (record.getFlowChannel()) {
            default -> createNormalOrder(record);
        };

        //生成订单绑卡记录
        orderCardService.genOrderCardRecord(order);
        //agreementService.applyRegisterSign(order.getRiskId());
        return order;
    }

    /**
     * 创建订单
     *
     * @param record 风控记录
     * @return order
     */
    private Order createNormalOrder(UserRiskRecord record) {
        String riskId = record.getId();
        String userId = record.getUserId();
        FlowChannel flowChannel = record.getFlowChannel();
        UserInfo userInfo = userService.findUserInfo(userId);
        PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElseThrow();
        //查询是否已存在订单
        Order order = orderRepository.findByRiskId(riskId);
        if (Objects.nonNull(order)) {
            warningService.warn("该风控记录:" + riskId + " 已存在关联订单", logger::error);
            throw new RuntimeException("该风控记录已存在关联订单");
        }
        //初始化订单
        order = new Order();
        order.setApproveRightsForce(record.getApproveRightsForce());
        order.setUserId(userId);
        order.setFlowChannel(flowChannel);
        order.setOpenId(preOrder.getOpenId());
        order.setOuterOrderId(preOrder.getOrderNo());
        order.setApplyTime(LocalDateTime.now());
        order.setApplyAmount(preOrder.getApplyAmount());
        order.setOrderState(OrderState.AUDIT_PASS);
        order.setApplyPeriods(preOrder.getApplyPeriods());
        order.setAmountType(preOrder.getAmountType());
        order.setRenewedFlag(WhetherState.N);
        order.setApproveAmount(preOrder.getApplyAmount());
        // 传入projectCode
        order.setProjectCode(preOrder.getProjectCode());
        // 建议额度
        BigDecimal approveAmount = preOrder.getApplyAmount();
        // 审批权益
        RightsLevel approveRights = record.getApproveRights();
        order.setApproveRights(approveRights);
        order.setApplicationSource(ApplicationSource.APP);
        order.setRightsPackageId(StringUtils.EMPTY);
        //order.setApplyChannel(ApplyChannel.QHYP_ZR); // 往绿信兼容

        order.setApplyChannel(record.getApplyChannel());
        // 审批利率
        RateLevel approveRate = record.getApproveRate();
        order.setApproveRate(approveRate);
        order.setIrrRate(approveRate.getRate());

        List<RepayPlan> repayPlans = PlanGenerator.genPlan(
                InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST,
                approveAmount,
                approveRate.getRate(),
                order.getApplyPeriods()
        );
        if (!CollectionUtils.isEmpty(repayPlans)) {
            RepayPlan plan = repayPlans.get(0);
            order.setMonthPay(plan.getPrincipal().add(plan.getInterest()));
        }
        order.setBindCapitalCardState(WhetherState.N);
        order.setOrderSubmitState(WhetherState.N);
        order.setRightsMarking(WhetherState.N);
        order.setName(userInfo.getName());
        order.setMobile(userInfo.getMobile());
        order.setCertNo(userInfo.getCertNo());
        //借款用途
        //convertLoanPurpose(flowChannel, userId, order, preOrder);
        logger.info("preOrder:{}", JSONObject.toJSONString(preOrder));
        if (WhetherState.Y.equals(preOrder.getIsAssignBankChannel())){
            order.setBankChannel(preOrder.getBankChannel());
        }
        //保存是不是权益客户
        if(FlowChannel.LVXIN.equals(order.getFlowChannel()) || FlowChannel.FQLQY001.equals(order.getFlowChannel())){
            order.setIsIncludingEquity(preOrder.getIsIncludingEquity());
            order.setEquityRecipient(preOrder.getEquityRecipient());
        }
        return orderRepository.save(order);
    }


    /**
     * 创建订单
     *
     * @param record 风控记录
     * @return order
     */
    private Order createNormalOrder(UserRiskRecordExternal record) {
        String riskId = record.getId();
        String userId = record.getUserId();
        FlowChannel flowChannel = record.getFlowChannel();
        UserInfo userInfo = userService.findUserInfo(userId);
        PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElseThrow();
        //查询是否已存在订单
        Order order = orderRepository.findByRiskId(riskId);
        if (Objects.nonNull(order)) {
            warningService.warn("该风控记录:" + riskId + " 已存在关联订单", logger::error);
            throw new RuntimeException("该风控记录已存在关联订单");
        }
        //初始化订单
        order = new Order();
        order.setApproveRightsForce(record.getApproveRightsForce());
        order.setUserId(userId);
        order.setFlowChannel(flowChannel);
        order.setOpenId(preOrder.getOpenId());
        order.setOuterOrderId(preOrder.getOrderNo());
        order.setRiskId(riskId);
        order.setApplyTime(LocalDateTime.now());
        order.setApplyAmount(preOrder.getApplyAmount());
        order.setOrderState(OrderState.AUDIT_PASS);
        order.setApplyPeriods(preOrder.getApplyPeriods());
        order.setAmountType(preOrder.getAmountType());
        order.setRenewedFlag(WhetherState.N);
        order.setApproveAmount(preOrder.getApplyAmount());
        // 传入projectCode
        order.setProjectCode(preOrder.getProjectCode());
        // 建议额度
        BigDecimal approveAmount = preOrder.getApplyAmount();
        // 审批权益
        RightsLevel approveRights = record.getApproveRights();
        order.setApproveRights(approveRights);
        order.setApplicationSource(ApplicationSource.APP);
        order.setRightsPackageId(StringUtils.EMPTY);
        //order.setApplyChannel(ApplyChannel.QHYP_ZR); // 往绿信兼容

        order.setApplyChannel(record.getApplyChannel());
        // 审批利率
        RateLevel approveRate = record.getApproveRate();
        order.setApproveRate(approveRate);
        //利率重新获取
        Optional<PreOrder> preOrders = preOrderRepository.findByOrderNo(preOrder.getOrderNo());
        if (preOrders.isPresent()){
            String projectCode = preOrders.get().getProjectCode();
            ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(projectCode);
            String customerInterestRate = projectInfoVO.getElements().getCustomerInterestRate();
            order.setIrrRate(new BigDecimal(customerInterestRate));
        }else {
            order.setIrrRate(approveRate.getRate());
        }


        List<RepayPlan> repayPlans = PlanGenerator.genPlan(
            InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST,
            approveAmount,
            approveRate.getRate(),
            order.getApplyPeriods()
        );
        if (!CollectionUtils.isEmpty(repayPlans)) {
            RepayPlan plan = repayPlans.get(0);
            order.setMonthPay(plan.getPrincipal().add(plan.getInterest()));
        }
        order.setBindCapitalCardState(WhetherState.N);
        order.setOrderSubmitState(WhetherState.N);
        order.setRightsMarking(WhetherState.N);
        order.setName(userInfo.getName());
        order.setMobile(userInfo.getMobile());
        order.setCertNo(userInfo.getCertNo());

        //借款用途
        //convertLoanPurpose(flowChannel, userId, order, preOrder);
        logger.info("preOrder:{}", JSONObject.toJSONString(preOrder));
        if (WhetherState.Y.equals(preOrder.getIsAssignBankChannel())){
            order.setBankChannel(preOrder.getBankChannel());
        }
        //保存是不是权益客户
        if(FlowChannel.LVXIN.equals(order.getFlowChannel()) || FlowChannel.FQLQY001.equals(order.getFlowChannel())){
            order.setIsIncludingEquity(preOrder.getIsIncludingEquity());
            order.setEquityRecipient(preOrder.getEquityRecipient());
        }
        return orderRepository.save(order);
    }

    private void convertLoanPurpose(FlowChannel flowChannel, String userId, Order order, PreOrder preOrder) {
        if (FlowChannel.isCommonApi(flowChannel)) {
            //借款用途
            ApprovalRecord approvalRecord = approvalRecordRepository.findByPartnerOrderNoAndFlowChannel(order.getOuterOrderId(), flowChannel).orElseThrow();
            order.setLoanPurpose(approvalRecord.getLoanPurpose());
            return;
        }
        switch (flowChannel) {

            case LVXIN -> {
                //借款用途
                LvxinApplyRecord lvxinApplyRecord = lvxinApplyRecordRepository.findByOrderNo(preOrder.getOrderNo()).orElseThrow();
                order.setLoanPurpose(LvxinBaseConvert.toLoanPurpose(lvxinApplyRecord.getLoanPurpose()));
            }
            default -> {
            }
        }
    }

}
