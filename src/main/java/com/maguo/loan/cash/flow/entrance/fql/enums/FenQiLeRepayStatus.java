package com.maguo.loan.cash.flow.entrance.fql.enums;

/**
 * <AUTHOR>
 * @Description 分期乐还款状态
 * @Date 2025/8/6 15:20
 * @Version v1.0
 **/
public enum FenQiLeRepayStatus {

    NORMAL(0, "未还款"),

    PROCESSING(1, "部分还款"),

    CLEAR(3, "已结清");

    private Integer code;

    private String desc;

    FenQiLeRepayStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
