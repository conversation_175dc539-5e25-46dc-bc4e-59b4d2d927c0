package com.maguo.loan.cash.flow.util;


import com.maguo.loan.cash.flow.dto.UserLoginInfo;

/**
 * <AUTHOR>
 * @since 2024-10-16
 */
public class UserUtils {

    private static ThreadLocal<UserLoginInfo> userInfoThreadLocal = new ThreadLocal<>();

    public static UserLoginInfo getUserLoginInfo() {
        return userInfoThreadLocal.get();
    }

    public static void setUserLoginInfo(UserLoginInfo loginInfo) {
        userInfoThreadLocal.set(loginInfo);
    }

    public static void removeUserLoginInfo() {
        userInfoThreadLocal.remove();
    }

}
