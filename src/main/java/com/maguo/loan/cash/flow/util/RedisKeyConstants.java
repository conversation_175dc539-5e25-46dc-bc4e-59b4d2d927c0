package com.maguo.loan.cash.flow.util;


/**
 * <AUTHOR>
 * @date 2024/1/16
 */
public class RedisKeyConstants {
    public static final String SPLIT_CHAR = ":";
    /**
     * PARENT
     */
    private static final String PARENT_L = "cash_business:";

    public static final String BIZ_LIMIT_LOAN = PARENT_L + "limit" + SPLIT_CHAR + "loan" + SPLIT_CHAR; // 放款限额

    public static final String SUSPEND_ACTIVE_LOAN = PARENT_L + "suspend_active" + SPLIT_CHAR + "loan" + SPLIT_CHAR; // 放款挂起激活

    public static final String APPROVAL_APPLY = "approval_apply:";

    public static final String SUBMIT_LOAN = "submit_loan:";

    public static final String REPAY_APPLY = "repay_apply:";

    public static final String ORDER_SUBMIT = "order_submit:";
    public static final String LOAN_DAY_FLOW_CHANNEL_LIMIT_LOCK = PARENT_L + "limit_flow_day_channel_lock" + SPLIT_CHAR + "loan" + SPLIT_CHAR; // 放款日限额

    public static final String LOAN_DAY_FLOW_CHANNEL_LIMIT = PARENT_L + "limit_flow_day_channel" + SPLIT_CHAR + "loan" + SPLIT_CHAR; // 放款日限额
    public static final String LOAN_DAY_FLOW_CHANNEL_LIMIT_ENABLE = PARENT_L + "limit_flow_day_channel_enable" + SPLIT_CHAR + "loan" + SPLIT_CHAR; // 放款日限额

    public static final String RISK_DECISION_KEY= PARENT_L + "risk_decision_key" ; // 风控规则配置项

}
