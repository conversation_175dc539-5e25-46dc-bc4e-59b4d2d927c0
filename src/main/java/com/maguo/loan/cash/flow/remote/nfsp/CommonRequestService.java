package com.maguo.loan.cash.flow.remote.nfsp;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jinghang.common.http.HttpFramework;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.remote.nfsp.req.CommonBaseReq;
import com.maguo.loan.cash.flow.remote.nfsp.req.SmsSendReq;
import com.maguo.loan.cash.flow.remote.nfsp.rsp.NfspCommonResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Service
public class CommonRequestService {

    private static final Logger logger = LoggerFactory.getLogger(CommonRequestService.class);
    @Value("${common.service.privateKey}")
    private String privateKey;
    @Value("${common.service.clientId}")
    private String clientId;
    @Value("${common.service.url}")
    private String url;

    public <T> T request(CommonBaseReq request, TypeReference<T> type) throws HttpException, JsonProcessingException, NfspBizException {
        try {
            logger.info("请求公共报文:{}", JsonUtil.convertToString(request));
            String rspStr = HttpUtil.post(HttpFramework.HTTPCLIENT5, url + request.getRequestType().getPath(), signRequest(request));
            //
            logger.info("公共返回原始报文:{}", rspStr);
            NfspCommonResponse response = JsonUtil.convertToObject(rspStr, NfspCommonResponse.class);
            if (!NfspCommonResponse.SUCCESS_CODE.equals(response.getCode())) {
                logger.error("公共接口异常:{}", JsonUtil.convertToString(response));
                if (Objects.equals(response.getCode(), NfspCommonResponse.ERROR_CODE) && Objects.equals(response.getMessage(), "订单不存在")) {
                    throw NfspBizException.NOT_EXIST;
                }
                throw new NfspBizException(response.getCode(), response.getMessage());
            }
            //
            String commonData = JsonUtil.convertToString(response.getData());
            logger.info("公共返回Data:{}", commonData);
            return JsonUtil.convertToCollection(commonData, type);
        } catch (HttpException e) {
            logger.error("请求nfsp-common,http异常", e);
            throw e;
        } catch (JsonProcessingException e) {
            logger.error("请求nfsp-common,json解析异常", e);
            throw e;
        }
    }


    private String signRequest(Object object) {
        JSONObject request = new JSONObject();
        request.put("pubMerNo", clientId);
        request.put("pubVersion", "1.0");
        request.put("pubNotifyUrl", "");
        String pubDataStr = JSON.toJSONString(object, JSONWriter.Feature.MapSortField);
        // logger.info("1111111==={}", pubDataStr);
        String pubSign = null;
        try {
            pubSign = NfspRSAUtils.sign(pubDataStr, privateKey);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        request.put("pubSign", pubSign);
        request.put("pubData", object);
        return request.toJSONString();
    }

    private static final String PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMam37JCZUrzkWxSaB+E4stVaXsssVCpX/IG+96nUjRPbIOux7T0cKWMxIXlTRWN"
        + "65hrC/IrnNL+iJZq7rhm1lC0RkaGikL8cc1GWflDNtghItOnwa40+RFP430KC3gbZ7KxGUbSxeier1WkqPrLXQHBLTJ2gnm91pFFzHfcWLrLAgMBAAECgYASq/y6tKLwuJQiqlw/Gi53DkAr47e"
        + "G7WSnSyDBfNitMgnxFqyOxasQ5HpUW4kzTXOnj+g/ivnimyobeVGW45OhN8xjDo8WZG3xS41iC2wBHr6SIjT6xjOtis2N4HW3FHFtOM3R0JP+UXgwWqeEvzMhCjdaBfj6PKJgAG1MiMBFAQJBAPN"
        + "hKHsVwVc0gdMcSZ0t7MyqWKIg1gO3Umf77NPKAgq4OqEsRXvwzQ3S1gTf1Bwqf/xzJsZ3g44YmOZSXVEtVZMCQQDQ8/WxmYl6gOFUtXlsRD4ACWZgbcUcCxDBisbRs4RFhta5Bn8UGPoXkp1tlqQ"
        + "T8Ksa0c6f9azPd6B8Lm1oucjpAkBqt+KpPhveIj7/E/tPebDI+bUNuno9fOgcgoIRSBXnH2tN+vyo4Sj488c2sfLvOs+OIewRwJwl9bEt5VXX35oTAkAazQ/Yhc8CbYF5cgVvA5nIO2xsNegwXqL"
        + "5kClMQHBY0qC7/r9R/polZ+LVaZsGC7qGqB6Omd5ehgOJqhLSefHxAkEA0uvFbCnsVCMyL/+T+UrzOxVAa/fv4YM4S+/TcGD/dwzoX084JGj93feT7GSgOhoGePxQJZnOaDUehW/0BWaAFw==";
    private static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDGpt+yQmVK85FsUmgfhOLLVWl7LLFQqV/yBvvep1I0T2yDrse09HCljMSF5U0VjeuYawvyK5z"
        + "S/oiWau64ZtZQtEZGhopC/HHNRln5QzbYISLTp8GuNPkRT+N9Cgt4G2eysRlG0sXonq9VpKj6y10BwS0ydoJ5vdaRRcx33Fi6ywIDAQAB";

    public static void main(String[] args) throws Exception {
        String jsonStr = "{\"type\":\"2\",\"context\":null,\"sign\":null,\"templateId\":\"QH026\",\"messageParamMap\":{\"bankCard\":\"8366\",\"month\":\"11\","
            + "\"name\":\"朱长萍\",\"eachAmount\":\"359.00\",\"day\":\"25\"},\"phoneList\":[\"***********\"]}";

        SmsSendReq smsSendReq = JSONObject.parseObject(jsonStr, SmsSendReq.class);

        String pubSingData = JSON.toJSONString(smsSendReq, JSONWriter.Feature.MapSortField);

        System.out.println(pubSingData);
        String pubSign = NfspRSAUtils.sign(pubSingData, PRIVATE_KEY);

        System.out.println(pubSign);

        System.out.println(NfspRSAUtils.verifyJSON(PUBLIC_KEY, pubSign, pubSingData));
    }
}
