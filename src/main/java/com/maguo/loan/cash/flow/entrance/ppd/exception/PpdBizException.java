package com.maguo.loan.cash.flow.entrance.ppd.exception;


import java.io.Serial;

/**
 * 自定异常类
 */
public class PpdBizException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1431155259402142272L;

    private final PpdResultCode resultCode;

    public PpdBizException(String message) {
        super(message);
        this.resultCode = PpdResultCode.SYSTEM_ERROR;
    }

    public PpdBizException(PpdResultCode resultCode) {
        super(resultCode.getMsg());
        this.resultCode = resultCode;
    }

    public PpdBizException(String message, PpdResultCode resultCode) {
        super(message);
        this.resultCode = resultCode;
    }

    public PpdResultCode getResultCode() {
        return resultCode;
    }

}
