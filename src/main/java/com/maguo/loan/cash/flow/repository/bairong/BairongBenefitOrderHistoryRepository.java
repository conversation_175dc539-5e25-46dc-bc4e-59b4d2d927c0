package com.maguo.loan.cash.flow.repository.bairong;

import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrder;
import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrderHistory;
import org.springframework.data.jpa.repository.JpaRepository;

public interface BairongBenefitOrderHistoryRepository extends JpaRepository<BairongBenefitOrderHistory, String> {

    /**
     * 根据渠道放款流水号查询订单列表
     *
     * @param outLoanSeq 渠道放款流水号
     * @return 订单列表
     */
    BairongBenefitOrder findByOutLoanSeq(String outLoanSeq);
}
