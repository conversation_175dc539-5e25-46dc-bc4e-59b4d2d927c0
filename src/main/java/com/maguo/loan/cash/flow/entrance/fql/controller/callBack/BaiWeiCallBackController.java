package com.maguo.loan.cash.flow.entrance.fql.controller.callBack;

import com.maguo.loan.cash.flow.entrance.fql.controller.FqlApiValidator;
import com.maguo.loan.cash.flow.entrance.fql.dto.callBack.BaiWeiDisburseRiskResultNoticeRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.callBack.BaiWeiInvestorsAuditResultQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.callBack.BaiWeiInvestorsAuditResultQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditResultNoticeRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditResultNoticeResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.InvestorsCreditResultNoticeRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.InvestorsCreditResultNoticeResponse;
import com.maguo.loan.cash.flow.entrance.fql.service.BaiWeiCallBackService;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 百维回调通知接口类
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/baiWei/callBack/api")
public class BaiWeiCallBackController extends FqlApiValidator {

    private static final Logger logger = LoggerFactory.getLogger(BaiWeiCallBackController.class);

    @Autowired
    private BaiWeiCallBackService baiWeiCallBackService;

    /**
     * 支用风控审核结果通知
     *
     * @param request 请求参数
     */
    @PostMapping("/disburseRiskResultNotice")
    public void disburseRiskResultNotice(@RequestBody @Valid BaiWeiDisburseRiskResultNoticeRequest request, BindingResult bindingResult) {
        // 必填参数校验
        validate(bindingResult);
        // 业务逻辑
        baiWeiCallBackService.disburseRiskResultNotice(request);
    }

    /**
     * 资方放款申请风控审核结果查询
     *
     * @param request 请求参数
     * @return 返回结果
     */
    @PostMapping("/auditResultQuery")
    public BaiWeiInvestorsAuditResultQueryResponse auditResultQuery(@RequestBody @Valid BaiWeiInvestorsAuditResultQueryRequest request,
                                                                    BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            // 业务逻辑
            return baiWeiCallBackService.auditResultQuery(request);
        } catch (Exception e) {
            logger.error("百维资方支用风控审核结果查询失败", e);
            return BaiWeiInvestorsAuditResultQueryResponse.fail("百维资方支用风控审核结果查询失败，系统异常");
        }
    }


    //单笔单批授信风控审核结果通知
    @PostMapping("/CreditResultNotice")
    public CreditResultNoticeResponse CreditResultNotice(@RequestBody @Valid CreditResultNoticeRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            //验证
            ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getPartnerCode());
            if (applyChannel == null) {
                CreditResultNoticeResponse response = new CreditResultNoticeResponse();
                response.setStatus(1);
                response.setMsg("未知渠道号");
                return response;
            }
            // 业务逻辑
            return baiWeiCallBackService.CreditResultNotice(request);
        }catch (Exception e){
            logger.error("授信申请的结果查询失败", e);
            return CreditResultNoticeResponse.fail("查询失败，系统异常");
        }
    }

    //资方授信风控结果查询
    @PostMapping("/InvestorsCreditResultNotice")
    public InvestorsCreditResultNoticeResponse InvestorsCreditResultNotice(@RequestBody @Valid InvestorsCreditResultNoticeRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            //验证
            ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getPartnerCode());
            if (applyChannel == null) {
                InvestorsCreditResultNoticeResponse response = new InvestorsCreditResultNoticeResponse();
                response.setStatus(1);
                response.setMsg("未知渠道号");
                return response;
            }
            // 业务逻辑
            return baiWeiCallBackService.InvestorsCreditResultNotice(request);
        }catch (Exception e){
            logger.error("授信申请的结果查询失败", e);
            return InvestorsCreditResultNoticeResponse.fail("查询失败，系统异常");
        }
    }

}
