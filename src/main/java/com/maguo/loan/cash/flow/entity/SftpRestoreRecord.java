package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

/**
 * sftp文件恢复记录
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "sftp_restore_record")
public class SftpRestoreRecord extends BaseEntity {
    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 外部订单流水号
     */
    private String outerOrderId;
    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * sftp文件地址
     */
    private String sftpPath;

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getOuterOrderId() {
        return outerOrderId;
    }

    public void setOuterOrderId(String outerOrderId) {
        this.outerOrderId = outerOrderId;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType file_type) {
        this.fileType = file_type;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public String getSftpPath() {
        return sftpPath;
    }

    public void setSftpPath(String sftpPath) {
        this.sftpPath = sftpPath;
    }

    @Override
    protected String prefix() {
        return "SFTP";
    }
}
