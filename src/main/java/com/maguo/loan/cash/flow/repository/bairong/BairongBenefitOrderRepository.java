package com.maguo.loan.cash.flow.repository.bairong;

import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrder;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;

public interface BairongBenefitOrderRepository extends JpaRepository<BairongBenefitOrder, String> {

    /**
     * 根据渠道放款流水号查询订单列表
     *
     * @param outLoanSeq 渠道放款流水号
     * @return 订单列表
     */
    BairongBenefitOrder findByOutLoanSeq(String outLoanSeq);

    /**
     * 查询创建时间在指定时间之前且状态不在指定列表中的订单
     *
     * @param createTime 创建时间
     * @param statusList 状态列表
     * @return 订单列表
     */
    List<BairongBenefitOrder> findByCreateTimeBeforeAndStatusNotIn(LocalDateTime createTime, List<Integer> statusList);
}
