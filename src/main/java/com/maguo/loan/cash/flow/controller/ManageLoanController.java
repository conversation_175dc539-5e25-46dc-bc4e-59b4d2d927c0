package com.maguo.loan.cash.flow.controller;

import com.jinghang.ppd.api.LoanApi;
import com.jinghang.ppd.api.dto.RestResult;
import com.maguo.loan.cash.flow.service.LoanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("manage/loan")
public class ManageLoanController implements LoanApi {

    @Autowired
    private LoanService loanService;

    @Override
    public RestResult<Void> loanFail(List<String> loanIds) {
        loanService.loanFail(loanIds);
        return RestResult.success(null);
    }

    @Override
    public RestResult<Void> loanRoute(List<String> loanIds) {
        loanService.loanRoute(loanIds);
        return RestResult.success(null);
    }

    @Override
    public RestResult<Void> reapplyLoan(List<String> loanRecordIds) {
        loanService.reapplyLoan(loanRecordIds);
        return RestResult.success(null);
    }
}
