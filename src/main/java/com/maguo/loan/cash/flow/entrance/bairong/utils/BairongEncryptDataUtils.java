package com.maguo.loan.cash.flow.entrance.bairong.utils;

import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongRequestData;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongResponseData;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderQueryRequest;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.cybk.filter.utils.AESUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/10 14:54
 */
public class BairongEncryptDataUtils {

    private static final Logger logger = LoggerFactory.getLogger(BairongEncryptDataUtils.class);

    /**
     * 验证签名并解密数据
     */
    public static boolean checkSignAndDecrypt(BairongRequestData encryptData, String aesKey, String signKey) {
        try {
            // 构建待验签字符串：channel + json + appId + randomStr
            String signContent = encryptData.getChannel() + encryptData.getJson() +
                encryptData.getAppId() + encryptData.getRandomStr();

            // 使用MD5+盐值进行签名验证
            String expectedSign = generateSign(signContent, signKey);

            boolean signResult = expectedSign.equals(encryptData.getSign());
            logger.info("签名验证结果: {}, 期望签名: {}, 实际签名: {}", signResult, expectedSign, encryptData.getSign());

            return signResult;
        } catch (Exception e) {
            logger.error("签名验证失败", e);
            return false;
        }
    }

    /**
     * 验证签名并解密数据
     */
    public static boolean checkSignAndDecrypt(BairongResponseData encryptData, String aesKey, String signKey) {
        try {
            // 构建待验签字符串：channel + json + appId + randomStr
            String signContent = encryptData.getChannel() + encryptData.getJson() +
                encryptData.getAppId() + encryptData.getRandomStr();

            // 使用MD5+盐值进行签名验证
            String expectedSign = generateSign(signContent, signKey);

            boolean signResult = expectedSign.equals(encryptData.getSign());
            logger.info("签名验证结果: {}, 期望签名: {}, 实际签名: {}", signResult, expectedSign, encryptData.getSign());

            return signResult;
        } catch (Exception e) {
            logger.error("签名验证失败", e);
            return false;
        }
    }

    /**
     * 解密JSON数据
     */
    public static String decryptJson(String encryptedJson, String aesKey) {
        try {
            // Base64解码
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedJson);

            // AES解密
            byte[] decryptedBytes = AESUtil.decrypt(encryptedBytes, aesKey);

            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("JSON解密失败", e);
            throw BairongException.DECRYPT_FAIL;
        }
    }

    /**
     * 加密并签名响应数据
     */
    public static void signAndEncrypt(BairongResponseData responseData, String aesKey, String signKey) {
        try {
            // AES加密响应JSON
            byte[] jsonBytes = responseData.getJson().getBytes(StandardCharsets.UTF_8);
            byte[] encryptedBytes = AESUtil.encrypt(jsonBytes, aesKey);
            String encryptedJson = Base64.getEncoder().encodeToString(encryptedBytes);
            responseData.setJson(encryptedJson);

            // 设置时间戳
            responseData.setRandomStr(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 生成签名
            String signContent = responseData.getChannel() + responseData.getJson() +
                responseData.getAppId() + responseData.getRandomStr();
            String sign = generateSign(signContent, signKey);
            responseData.setSign(sign);

            logger.info("响应数据加密签名完成");
        } catch (Exception e) {
            logger.error("响应数据加密签名失败", e);
            throw BairongException.ENCRYPT_FAIL;
        }
    }

    /**
     * 加密并签名请求数据
     */
    public static String signAndEncrypt(BairongBenefitOrderQueryRequest request, String aesKey, String signKey) {
        BairongRequestData bairongRequestData = new BairongRequestData();
        try {
            // AES加密响应JSON
            String encryptedBytes = AESUtil.encrypt(JsonUtil.toJsonString(request), aesKey);
            String encryptedJson = Base64.getEncoder().encodeToString(encryptedBytes.getBytes(StandardCharsets.UTF_8));
            bairongRequestData.setJson(encryptedJson);

            // 设置时间戳
            bairongRequestData.setRandomStr(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 生成签名
            String signContent = bairongRequestData.getChannel() + bairongRequestData.getJson() +
                bairongRequestData.getAppId() + bairongRequestData.getRandomStr();
            String sign = generateSign(signContent, signKey);
            bairongRequestData.setSign(sign);

            logger.info("响应数据加密签名完成");
        } catch (Exception e) {
            logger.error("响应数据加密签名失败", e);
            throw BairongException.ENCRYPT_FAIL;
        }
        return JsonUtil.toJsonString(bairongRequestData);
    }

    /**
     * 生成签名
     */
    private static String generateSign(String content, String signKey) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update((content + signKey).getBytes(StandardCharsets.UTF_8));
            byte[] digest = md.digest();

            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString().toUpperCase();
        } catch (Exception e) {
            logger.error("签名生成失败", e);
            throw new RuntimeException("签名生成失败", e);
        }
    }
}
