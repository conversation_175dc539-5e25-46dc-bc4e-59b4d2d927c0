package com.maguo.loan.cash.flow.job.repayplan;

import com.alibaba.fastjson2.JSON;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.isp.SmsTemplateFeign;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.xxl.job.core.handler.annotation.JobHandler;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler("repayPlanJob")
public class RepayPlanJob extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(RepayPlanJob.class);

    private static final String TEMPLATE_BEFORE = "0";
    private static final String TEMPLATE_TODAY = "1";
    private static final String TEMPLATE_AFTER = "2";
    private static final String TEMPLATE_OTHER = "3";

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private SmsTemplateFeign smsTemplateFeign;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Override
    public void doJob(JobParam jobParam) {

        int hour = LocalDateTime.now().getHour();
        // 条件构建
        RepayPlan repayPlan = new RepayPlan();
        repayPlan.setCustRepayState(RepayState.NORMAL);
        Example<RepayPlan> example = Example.of(repayPlan);
        //初始化批次参数
        int currentPage = 0;
        int pageSize = 300;

        PageRequest pageRequest = PageRequest.of(currentPage, pageSize);
        Page<RepayPlan> allRepayPlan = repayPlanRepository.findAll(example, pageRequest);

        int totalPage = allRepayPlan.getTotalPages();

        // 内存分页批次
        for (currentPage = 0; currentPage <= totalPage; currentPage++) {
            logger.info("当前还款提醒开始第：{} 批次处理，批次数据长度：{}.", currentPage + 1, allRepayPlan.getContent().size());
            List<RepayPlan> repayPlanList = allRepayPlan.getContent();

            // 加载批次处理数据
            if(currentPage > 0){
                PageRequest batchPageRequest = PageRequest.of(currentPage*pageSize, pageSize);
                Page<RepayPlan> loadBatchRepayPlan = repayPlanRepository.findAll(example, batchPageRequest);
                repayPlanList = loadBatchRepayPlan.getContent();
            }

            Map<String, List<RepayPlan>> groupRepayPlan = repayPlanList.stream().collect(Collectors.groupingBy(item -> {
                LocalDate planRepayDate = item.getPlanRepayDate();
                LocalDate today = LocalDate.now();
                long between = ChronoUnit.DAYS.between(planRepayDate, today);

                //M1	逾期1-3天	早上10点，下午3点和晚上6点，一天三次
                if (between > 0 && between <= 3) return TEMPLATE_AFTER;
                //M1	逾期3天以上	早上10点，下午3点和晚上6点，一天三次
                if (between > 3) return TEMPLATE_OTHER;

                // 如果是晚上18点不需要发送以下提醒
                if(hour == 18) return "error";
                // 还款日前三天	早上10点和下午3点，一天两次
                if (between >= -3 && between < 0) return TEMPLATE_BEFORE;
                // M0	还款日当天	早上10点和下午3点，一天两次
                if (between == 0) return TEMPLATE_TODAY;

                return "error";
            }));

            // 分组不同得用户使用不同得短信模板
            groupRepayPlan.forEach((templateId, list) -> {

                if(templateId.equals("error")) return;

                logger.info("开始处理批次为：{} 用户",templateId);
                list.forEach(itemRepayPlan -> {
                    Map<String, Object> params = new HashMap<>();
                    //用户信息
                    UserInfo userInfo = userInfoRepository.findById(itemRepayPlan.getUserId()).orElseThrow();
                    params.put("phone", userInfo.getMobile());
                    params.put("templateId", templateId);
                    params.put("name", userInfo.getName());
                    switch (templateId) {
                        case TEMPLATE_BEFORE:
                            params.put("amount", itemRepayPlan.getAmount());
                            break;
                        case TEMPLATE_TODAY:
                            params.put("date", itemRepayPlan.getPlanRepayDate());
                            params.put("amount", itemRepayPlan.getAmount());
                            break;
                        case TEMPLATE_AFTER:
                        case TEMPLATE_OTHER:
                            params.put("telephone", userInfo.getUnitPhone());
                            break;
                    }
                    logger.info("组装请求参数：{}", JSON.toJSONString(params));
                    Map<String, Object> stringObjectMap = smsTemplateFeign.sendSms(params);
                    logger.info("isp服务响应结果：{}", JSON.toJSONString(stringObjectMap));

                });
                logger.info("批次为：{} 用户处理完毕",templateId);
            });
            logger.info("对客还款计划 job任务结束");
        }
    }
}
