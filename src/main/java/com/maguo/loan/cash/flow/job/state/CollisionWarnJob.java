package com.maguo.loan.cash.flow.job.state;


import com.jinghang.common.util.DateUtil;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.CollisionRecordRepository;
import com.maguo.loan.cash.flow.service.WarningService;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * 撞库成功率预警job
 *
 * <AUTHOR>
 */
@Component
@JobHandler("collisionWarnJob")
public class CollisionWarnJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(CollisionWarnJob.class);

    @Resource
    private WarningService warningServiceDataService;

    /**
     * 告警时间范围
     */
    @Value("${collision.warn.minutes:10}")
    private Integer minutes;

    /**
     * 成功率
     */
    @Value("${collision.warn.successRate:0.8}")
    private BigDecimal successRate;

    @Autowired
    private CollisionRecordRepository collisionRecordRepository;

    @Override
    public void doJob(JobParam jobParam) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMinutes(minutes);
        int total = collisionRecordRepository.countByCreatedTimeIsBetween(startTime, endTime);
        if (total == 0) {
            return;
        }

        int success = collisionRecordRepository.countByCreatedTimeIsBetweenAndState(startTime, endTime, AuditState.PASS);

        //当前成功率
        BigDecimal currentSuccessRate = BigDecimal.valueOf(success).divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);

        if (currentSuccessRate.compareTo(successRate) < 0) {
            warningServiceDataService.warn(startTime.format(DateUtil.NEW_FORMATTER) + " ~ " + endTime.format(DateUtil.NEW_FORMATTER)
                + "\n" + "撞库成功率 : " + currentSuccessRate.movePointRight(2) + "%");
        }

    }
}
