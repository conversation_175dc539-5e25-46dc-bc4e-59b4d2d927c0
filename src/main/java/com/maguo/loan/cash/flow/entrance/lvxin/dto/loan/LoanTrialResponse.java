package com.maguo.loan.cash.flow.entrance.lvxin.dto.loan;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName LoanTrialResponse
 * <AUTHOR>
 * @Description 借款试算 响应参数
 * @Date 2024/5/21 11:05
 * @Version v1.0
 **/
public class LoanTrialResponse {
    /**
     * 应还总金额
     */
    private BigDecimal totalAmount;
    /**
     * 应还总本金
     */
    private BigDecimal totalPrincipal;
    /**
     * 应还总利息
     */
    private BigDecimal totalInterest;
    /**
     * 应还总手续费
     */
    private BigDecimal totalRightFee;

    //应还总咨询费
    private BigDecimal totalConsultFee;

    //应还总罚息
    private BigDecimal totalOverdue;
    /**
     * 还款计划
     */
    private List<LvxinRepayPlan> repayPlanList;
    /**
     * 资方标识
     */
    private String lender;
    /**
     * 年化利率 34.87%
     */
    private String executeRate;
    public List<LvxinRepayPlan> getRepayPlanList() {
        return repayPlanList;
    }

    public void setRepayPlanList(List<LvxinRepayPlan> repayPlanList) {
        this.repayPlanList = repayPlanList;
    }


    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalPrincipal() {
        return totalPrincipal;
    }

    public void setTotalPrincipal(BigDecimal totalPrincipal) {
        this.totalPrincipal = totalPrincipal;
    }

    public BigDecimal getTotalInterest() {
        return totalInterest;
    }

    public void setTotalInterest(BigDecimal totalInterest) {
        this.totalInterest = totalInterest;
    }

    public BigDecimal getTotalRightFee() {
        return totalRightFee;
    }

    public void setTotalRightFee(BigDecimal totalRightFee) {
        this.totalRightFee = totalRightFee;
    }

    public BigDecimal getTotalConsultFee() {
        return totalConsultFee;
    }

    public void setTotalConsultFee(BigDecimal totalConsultFee) {
        this.totalConsultFee = totalConsultFee;
    }

    public BigDecimal getTotalOverdue() {
        return totalOverdue;
    }

    public void setTotalOverdue(BigDecimal totalOverdue) {
        this.totalOverdue = totalOverdue;
    }

    public String getLender() {
        return lender;
    }

    public void setLender(String lender) {
        this.lender = lender;
    }

    public String getExecuteRate() {
        return executeRate;
    }

    public void setExecuteRate(String executeRate) {
        this.executeRate = executeRate;
    }
}
