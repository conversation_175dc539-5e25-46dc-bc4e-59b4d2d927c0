package com.maguo.loan.cash.flow.enums;

/**
 * 来源广告
 *
 * <AUTHOR>
 * @date 2024/5/24
 */
public enum ApplyChannel {

    QHYP_BORUI("qhyp_borui", "博瑞"),
    QHYP_TANZHI("tanzhi", "探知流量"),
    /**
     * 没有设备号的使用，兼容老数据
     */
    QHYP_ZR("qhyp_zr", "其他"),
    LVXIN("01", "绿信"),
    QHYP_GIO("gio", "gio营销"),
    CJCYDL_PPD2("p002","拍拍贷-低定价客群"),
    CJCYDL_PPD3("p001","拍拍贷-高定价客群"),
    CJHBXJ_PPD2("p002","拍拍贷-低定价客群"),
    CJHBXJ_PPD3("p001","拍拍贷-高定价客群"),
    FQLQY001("fqlqy001","分期乐贷款"),
    LTFQ("ltfq", "乐通分期"),
    ;


    private final String code;
    private final String desc;

    ApplyChannel(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }


    public static ApplyChannel getApplyChannel(String name) {
        for (ApplyChannel applyChannel : ApplyChannel.values()) {
            if (applyChannel.name().equals(name)) {
                return applyChannel;
            }
        }
        return null;
    }

    public static ApplyChannel getApplyChannelByCode(String code) {
        for (ApplyChannel applyChannel : ApplyChannel.values()) {
            if (applyChannel.getCode().equals(code)) {
                return applyChannel;
            }
        }
        return null;
    }
}
