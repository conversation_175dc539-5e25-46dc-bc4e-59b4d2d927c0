package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.OverdueFlagEnum;
import com.maguo.loan.cash.flow.enums.RepayState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "repay_plan")
public class RepayPlan extends BaseEntity {
    private String userId;
    /**
     * 借据id
     */
    private String loanId;
    /**
     * 期次
     */
    private Integer period;
    /**
     * 计划还款日
     */
    private LocalDate planRepayDate;
    /**
     * 应还本金
     */
    private BigDecimal principalAmt;
    /**
     * 应还利息（银行）
     */
    private BigDecimal interestAmt;
    /**
     * 应还担保费
     */
    private BigDecimal guaranteeAmt;
    /**
     * 应还罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 应还总金额
     */
    private BigDecimal amount;
    /**
     * 应还咨询费
     */
    private BigDecimal consultFee;
    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    private RepayState custRepayState;
    /**
     * 实还时间
     */
    private LocalDateTime actRepayTime;
    /**
     * 实还本金
     */
    private BigDecimal actPrincipalAmt;
    /**
     * 实还利息（银行）
     */
    private BigDecimal actInterestAmt;
    /**
     * 实还担保费
     */
    private BigDecimal actGuaranteeAmt;
    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt;
    /**
     * 实还资方罚息
     */
    private BigDecimal actCapitalPenaltyAmt;
    /**
     * 实还违约金
     */
    private BigDecimal actBreachAmt;
    /**
     * 实还总额
     */
    private BigDecimal actAmount;
    /**
     * 实还咨询费
     */
    private BigDecimal actConsultFee;
    /**
     * 逾期标记字段（overdue_flag）：overdue：逾期、normal：正常，逾期标记逻辑：超过到期日转逾期，但不计罚息，宽限期外补收宽限期内所有罚息
     */
    @Enumerated(EnumType.STRING)
    private OverdueFlagEnum overdueFlag;

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getActConsultFee() {
        return actConsultFee;
    }

    public void setActConsultFee(BigDecimal actConsultFee) {
        this.actConsultFee = actConsultFee;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public LocalDate getPlanRepayDate() {
        return planRepayDate;
    }

    public void setPlanRepayDate(LocalDate planRepayDate) {
        this.planRepayDate = planRepayDate;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }


    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public RepayState getCustRepayState() {
        return custRepayState;
    }

    public void setCustRepayState(RepayState custRepayState) {
        this.custRepayState = custRepayState;
    }

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActGuaranteeAmt() {
        return actGuaranteeAmt;
    }

    public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
        this.actGuaranteeAmt = actGuaranteeAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActBreachAmt() {
        return actBreachAmt;
    }

    public void setActBreachAmt(BigDecimal actBreachAmt) {
        this.actBreachAmt = actBreachAmt;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    public BigDecimal getActCapitalPenaltyAmt() {
        return actCapitalPenaltyAmt;
    }

    public void setActCapitalPenaltyAmt(BigDecimal actCapitalPenaltyAmt) {
        this.actCapitalPenaltyAmt = actCapitalPenaltyAmt;
    }

    public OverdueFlagEnum getOverdueFlag() {
        return overdueFlag;
    }

    public void setOverdueFlag(OverdueFlagEnum overdueFlag) {
        this.overdueFlag = overdueFlag;
    }

    @Override
    protected String prefix() {
        return "RP";
    }
}
