package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.PartnerRightsConfig;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/2
 */
public interface PartnerRightsConfigRepository extends JpaRepository<PartnerRightsConfig, String> {
    List<PartnerRightsConfig> findAllByEnable(AbleStatus enable);
}
