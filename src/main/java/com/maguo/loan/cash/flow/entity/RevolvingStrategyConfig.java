package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * 额度策略配置表
 */
@Entity
@Table(name = "revolving_strategy_config")
public class RevolvingStrategyConfig extends BaseEntity {

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略编码
     */
    private String strategyCode;

    /**
     * 额度区间上限
     */
    private Integer amountUpper;

    /**
     * 额度区间下限
     */
    private Integer amountLower;

    /**
     * 策略状态（启用/禁用）
     */
    @Enumerated(EnumType.STRING)
    private WhetherState strategyState;

    public String getStrategyName() {
        return strategyName;
    }

    public void setStrategyName(String strategyName) {
        this.strategyName = strategyName;
    }

    public String getStrategyCode() {
        return strategyCode;
    }

    public void setStrategyCode(String strategyCode) {
        this.strategyCode = strategyCode;
    }

    public Integer getAmountUpper() {
        return amountUpper;
    }

    public void setAmountUpper(Integer amountUpper) {
        this.amountUpper = amountUpper;
    }

    public Integer getAmountLower() {
        return amountLower;
    }

    public void setAmountLower(Integer amountLower) {
        this.amountLower = amountLower;
    }

    public WhetherState getStrategyState() {
        return strategyState;
    }

    public void setStrategyState(WhetherState strategyState) {
        this.strategyState = strategyState;
    }

    @Override
    protected String prefix() {
        return "RSC";
    }
}
