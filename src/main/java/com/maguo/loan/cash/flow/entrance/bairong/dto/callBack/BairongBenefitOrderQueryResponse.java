package com.maguo.loan.cash.flow.entrance.bairong.dto.callBack;

import lombok.Data;
import java.util.List;

/**
 * 权益订单查询接口响应参数
 */
@Data
public class BairongBenefitOrderQueryResponse {
        /**
         * 权益方订单号（必填）
         */
        private String benefitOrderNo;

        /**
         * 订单金额（必填），单位：元
         */
        private Double orderAmount;

        /**
         * 订单状态（必填）
         * 1-待支付，2-支付成功，3-支付失败，4-退款中，5-退款成功，6-退款失败，7-订单取消
         */
        private Integer status;

        /**
         * 订单创建时间（必填），格式：毫秒时间戳
         */
        private Long createTime;

        /**
         * 支付时间（非必填），格式：毫秒时间戳，仅支付成功时返回
         */
        private Long payTime;

        /**
         * 支付流水号（非必填），仅支付成功时返回
         */
        private String paymentNo;

        /**
         * 支付通道（必填）
         * 可选值：BAOFU-宝付支付、ALLINPAY-通联支付、YEEPAY-易宝支付、JDPAY-京东支付、DXMPAY-度小满支付、KQIANPAY-快钱支付
         */
        private String paymentChannel;

        /**
         * 订单过期时间（非必填），格式：毫秒时间戳
         */
        private Long expireTime;

        /**
         * 退款金额（非必填），多笔退款时为总金额，单位：元
         */
        private Double refundAmount;

        /**
         * 机构分账金额（非必填），单位：元
         */
        private Double splitAccountAmount;

        /**
         * 机构多商户分账信息集合（非必填）
         */
        private List<PartnerShareInfo> partnerShareList;

        /**
         * 机构分账退款金额（非必填），单位：元
         */
        private Double shareRefundAmount;

        /**
         * 多次退款信息集合（非必填）
         */
        private List<RefundInfo> refundInfoList;

        /**
         * 支付失败原因（非必填），仅支付失败时返回最新一次扣款失败原因
         */
        private String failReason;

        /**
         * 权益服务商（必填）
         * 可选值：1-钛翮科技、2-众奚科技、3-及未科技
         */
        private String benefitServiceProvider;
    }


