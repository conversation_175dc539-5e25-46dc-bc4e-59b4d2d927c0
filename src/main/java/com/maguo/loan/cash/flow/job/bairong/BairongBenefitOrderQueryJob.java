package com.maguo.loan.cash.flow.job.bairong;

import com.alibaba.fastjson.JSON;
import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrder;
import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrderHistory;
import com.maguo.loan.cash.flow.entrance.bairong.config.BairongConfig;
import com.maguo.loan.cash.flow.entrance.bairong.convert.BairongConvert;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongResponseData;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderQueryRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderQueryResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.PartnerShareInfo;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.RefundInfo;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BenefitOrderStatusEnum;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;
import com.maguo.loan.cash.flow.entrance.bairong.utils.BairongEncryptDataUtils;
import com.maguo.loan.cash.flow.entrance.bairong.utils.http.HttpUtil;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.bairong.BairongBenefitOrderHistoryRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongBenefitOrderRepository;
import com.maguo.loan.cash.flow.service.WarningService;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

//百融权益订单状态查询
//我方主动发起，用于查询某笔权益订单的状态及分账详情
@Component
@JobHandler("BairongBenefitOrderQueryJob")
public class BairongBenefitOrderQueryJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(BairongBenefitOrderQueryJob.class);

    @Autowired
    private BairongBenefitOrderRepository bairongBenefitOrderRepository;

    @Autowired
    private BairongBenefitOrderHistoryRepository bairongBenefitOrderHistoryRepository;

    @Autowired
    private WarningService warningService;

    @Autowired
    private BairongConfig bairongConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doJob(JobParam jobParam) throws Exception {
        logger.info("开始执行百融权益订单查询定时任务");

        //1. 查询 bairong_benefit_order 表的 create_time 判断超过当前时间半小时且没有终态的权益订单
        List<BairongBenefitOrder> pendingOrders = findPendingOrders();

        logger.info("查询到{}笔超过半小时且未终态的权益订单", pendingOrders.size());

        //1.1 查询到的数据可能是多笔
        for (BairongBenefitOrder order : pendingOrders) {
            try {
                //2. 调百融的权益订单查询接口查询该笔权益订单状态（访问地址有我自己提供）
                BairongBenefitOrderQueryRequest queryRequest = new BairongBenefitOrderQueryRequest();
                queryRequest.setLoanNo(order.getLoanNo());
                queryRequest.setOutLoanSeq(order.getOutLoanSeq());

                //2.1 查询到的数据可能是多笔，需要一笔一笔去查（请求参数为 BairongBenefitOrderQueryRequest 表的：loanNo、outLoanSeq）
                BairongBenefitOrderQueryResponse queryResponse = callBairongQueryApi(queryRequest);

                if (Objects.nonNull(queryResponse)) {
                    //3. 查询的结果可以直接添加到 BairongBenefitOrderHistory 历史记录表中
                    BairongBenefitOrderHistory history = new BairongBenefitOrderHistory();
                    BairongConvert.INSTANCE.toBairongBenefitOrderHistory(history, queryResponse);
                    // 处理无法映射的字段
                    populateAdditionalInfo(history, queryResponse, queryRequest);
                    bairongBenefitOrderHistoryRepository.save(history);

                    //4. 查询的结果如果是终态更新 bairong_benefit_order 表的状态（根据 benefitOrderNo 字段做更新）
                    Integer status = queryResponse.getStatus();
                    if (isFinalStatus(status)) {
                        order.setStatus(status);
                        // 更新其他字段
                        order.setPayTime(queryResponse.getPayTime());
                        order.setPaymentNo(queryResponse.getPaymentNo());
                        order.setFailReason(queryResponse.getFailReason());
                        order.setSplitAccountAmount(queryResponse.getSplitAccountAmount());
                        order.setShareRefundAmount(queryResponse.getShareRefundAmount());
                        bairongBenefitOrderRepository.save(order);
                        logger.info("更新权益订单状态成功，订单号：{}，状态：{}", order.getBenefitOrderNo(), status);
                    } else {
                        //5. 查询的结果如果不是终态就企业微信告警通知
                        BenefitOrderStatusEnum statusEnum = BenefitOrderStatusEnum.getByCode(status);
                        String statusName = (Objects.nonNull(statusEnum)) ? statusEnum.getName() : String.valueOf(status);
                        warningService.warn("百融权益订单查询结果非终态，订单号：%s，状态：%s", order.getBenefitOrderNo(), statusName);
                        logger.warn("百融权益订单查询结果非终态，订单号：{}，状态：{}", order.getBenefitOrderNo(), statusName);
                    }
                }
            } catch (BairongException e) {
                logger.error("处理权益订单失败，订单号：" + order.getBenefitOrderNo(), e);
                // 记录错误但继续处理其他订单，不中断整个任务
                warningService.warn("处理百融权益订单失败，订单号：%s，错误信息：%s", order.getBenefitOrderNo(), e.getMessage());
            } catch (Exception e) {
                logger.error("处理权益订单时发生未预期异常，订单号：" + order.getBenefitOrderNo(), e);
                // 使用百融异常类包装普通异常
                BairongException bairongException = new BairongException(
                    BairongResultCode.EQUITY_ORDER_NOTIFY_SYSTEM_ERROR.getCode(),
                    "处理权益订单时发生系统错误: " + e.getMessage());
                warningService.warn("处理百融权益订单时发生未预期异常，订单号：%s，错误信息：%s",
                    order.getBenefitOrderNo(), bairongException.getMessage());
            }
        }

        logger.info("百融权益订单查询定时任务执行完成");
    }

    /**
     * 填充额外信息（PartnerShareInfo和RefundInfo列表数据）
     *
     * @param history       权益订单历史实体
     * @param queryResponse 权益订单查询响应参数
     */
    private void populateAdditionalInfo(BairongBenefitOrderHistory history, BairongBenefitOrderQueryResponse queryResponse,
                                        BairongBenefitOrderQueryRequest queryRequest) {
        history.setLoanNo(queryRequest.getLoanNo());
        history.setOutLoanSeq(queryRequest.getOutLoanSeq());
        // 处理PartnerShareInfo列表，取第一个元素（如果存在）
        if (Objects.nonNull(queryResponse.getPartnerShareList()) && !queryResponse.getPartnerShareList().isEmpty()) {
            PartnerShareInfo partnerShareInfo = queryResponse.getPartnerShareList().get(0);
            // 增加对列表元素的非空检查
            if (Objects.nonNull(partnerShareInfo)) {
                history.setPartnerShareMerchantNo(partnerShareInfo.getPartnerShareMerchantNo());
                history.setPartnerShareAmount(partnerShareInfo.getPartnerShareAmount());
                history.setPartnerShareRefundAmount(partnerShareInfo.getPartnerShareRefundAmount());
            }
        }

        // 处理RefundInfo列表，取第一个元素（如果存在）
        if (Objects.nonNull(queryResponse.getRefundInfoList()) && !queryResponse.getRefundInfoList().isEmpty()) {
            RefundInfo refundInfo = queryResponse.getRefundInfoList().get(0);
            // 增加对列表元素的非空检查
            if (Objects.nonNull(refundInfo)) {
                history.setRefundNo(refundInfo.getRefundNo());
                history.setRefundTime(refundInfo.getRefundTime());
                history.setRefundAmount(refundInfo.getRefundAmount());
                history.setRefundMethod(refundInfo.getRefundMethod());
            }
        }
    }

    /**
     * 查询超过半小时且未终态的权益订单
     *
     * @return 待处理的订单列表
     */
    private List<BairongBenefitOrder> findPendingOrders() {
        LocalDateTime halfHourAgo = LocalDateTime.now().minusMinutes(30);
        // 终态状态：2-支付成功、3-支付失败、5-退款成功、6-退款失败、7-订单取消
        List<Integer> finalStatusList = Arrays.asList(2, 3, 5, 6, 7);
        return bairongBenefitOrderRepository.findByCreateTimeBeforeAndStatusNotIn(halfHourAgo, finalStatusList);
    }

    /**
     * 调用百融权益订单查询接口
     *
     * @param request 查询请求参数
     * @return 查询响应结果
     */
    private BairongBenefitOrderQueryResponse callBairongQueryApi(BairongBenefitOrderQueryRequest request) throws BairongException {

        // 将请求对象转换为JSON字符串
        String requestBody = BairongEncryptDataUtils.signAndEncrypt(request, bairongConfig.getAesKey(), bairongConfig.getSignKey());

        // 发送HTTP请求
        Pair<Integer, String> response = HttpUtil.postByJson("/benefit/query/BRYC", requestBody);

        logger.info("调用百融权益订单查询接口，请求参数：loanNo={}, outLoanSeq={}, 状态码={}",
            request.getLoanNo(), request.getOutLoanSeq(), response.getLeft());

        // 检查响应状态码
        if (response.getLeft() != 200) {
            logger.error("调用百融权益订单查询接口失败，状态码：{}，响应内容：{}", response.getLeft(), response.getRight());
            throw new BairongException(
                BairongResultCode.EQUITY_ORDER_NOTIFY_SYSTEM_ERROR.getCode(),
                "调用百融权益订单查询接口失败，状态码：" + response.getLeft());
        }

        // 解析响应内容为 BairongResponseData
        try {
            BairongResponseData responseData = JSON.parseObject(response.getRight(), BairongResponseData.class);

            // 1. 校验签名
            boolean signValid = BairongEncryptDataUtils.checkSignAndDecrypt(
                responseData,
                bairongConfig.getAesKey(),
                bairongConfig.getSignKey()
            );

            if (!signValid) {
                logger.error("响应签名验证失败，响应内容：{}", response.getRight());
                throw new BairongException(
                    BairongResultCode.DECRYPT_FAIL.getCode(),
                    "响应签名验证失败");
            }

            // 2. 解密 json 字段
            String decryptedJson = BairongEncryptDataUtils.decryptJson(
                responseData.getJson(),
                bairongConfig.getAesKey()
                );

            // 3. 解析解密后的 JSON 为业务对象
            BairongBenefitOrderQueryResponse result = JSON.parseObject(decryptedJson, BairongBenefitOrderQueryResponse.class);
            logger.info("百融权益订单查询接口调用成功，返回结果：{}", decryptedJson);
            return result;

        } catch (Exception e) {
            logger.error("解析百融权益订单查询接口响应失败，响应内容：{}", response.getRight(), e);
            throw new BairongException(
                BairongResultCode.EQUITY_ORDER_NOTIFY_SYSTEM_ERROR.getCode(),
                "解析百融权益订单查询接口响应失败: " + e.getMessage());
        }
    }



    /**
     * 判断订单状态是否为终态
     *
     * @param status 订单状态
     * @return 是否为终态
     */
    private boolean isFinalStatus(Integer status) {
        // 终态状态：2-支付成功、3-支付失败、5-退款成功、6-退款失败、7-订单取消
        return Objects.nonNull(status) && Arrays.asList(2, 3, 5, 6, 7).contains(status);
    }
}
