package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.ChargeBizType;
import com.maguo.loan.cash.flow.enums.Payee;
import com.maguo.loan.cash.flow.enums.PaymentChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 外部代扣表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Entity
@Table(name = "out_withhold_flow")
public class OutWithholdFlow extends BaseEntity {

    /**
     * 还款记录id
     */
    private String repayRecordId;

    /**
     * 借据
     */
    private String loanId;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 扣款业务类型
     */
    @Enumerated(EnumType.STRING)
    private ChargeBizType bizType;

    /**
     * 期次
     */
    private Integer period;

    /**
     * 代扣金额
     */
    private BigDecimal payAmount;

    /**
     * 代扣协议号
     */
    private String agreementNo;

    /**
     * 扣款时间
     */
    private LocalDateTime payTime;

    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState payState;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 公共账户类型
     */
    private String commonWithholdType;

    /**
     * 渠道ID
     */
    @Enumerated(EnumType.STRING)
    private PaymentChannel channelId;

    /**
     * 渠道商户号
     */
    private String channelMchId;

    /**
     * 渠道流水号
     */
    private String channelOrderNo;

    /**
     * 代扣流水号
     */
    private String payOrderNo;

    /**
     * 收款方
     */
    @Enumerated(EnumType.STRING)
    private Payee payee;

    /**
     * 扣款主商户
     */
    private String withholdTypeMemberId;

    /**
     * 是否存在分账账户信息
     */
    @Enumerated(EnumType.STRING)
    private WhetherState shareInfo;
    /**
     * 还款模式 线下 线上
     */
    @Enumerated(EnumType.STRING)
    private RepayMode repayMode;

    /**
     * 目的
     */
    private String withholdPurpose;

    @Override
    protected String prefix() {
        return "OWF";
    }

    public String getRepayRecordId() {
        return repayRecordId;
    }

    public void setRepayRecordId(String repayRecordId) {
        this.repayRecordId = repayRecordId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public ChargeBizType getBizType() {
        return bizType;
    }

    public void setBizType(ChargeBizType bizType) {
        this.bizType = bizType;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public ProcessState getPayState() {
        return payState;
    }

    public void setPayState(ProcessState payState) {
        this.payState = payState;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getCommonWithholdType() {
        return commonWithholdType;
    }

    public void setCommonWithholdType(String commonWithholdType) {
        this.commonWithholdType = commonWithholdType;
    }

    public PaymentChannel getChannelId() {
        return channelId;
    }

    public void setChannelId(PaymentChannel channelId) {
        this.channelId = channelId;
    }

    public String getChannelMchId() {
        return channelMchId;
    }

    public void setChannelMchId(String channelMchId) {
        this.channelMchId = channelMchId;
    }

    public String getChannelOrderNo() {
        return channelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        this.channelOrderNo = channelOrderNo;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public Payee getPayee() {
        return payee;
    }

    public void setPayee(Payee payee) {
        this.payee = payee;
    }

    public String getWithholdTypeMemberId() {
        return withholdTypeMemberId;
    }

    public void setWithholdTypeMemberId(String withholdTypeMemberId) {
        this.withholdTypeMemberId = withholdTypeMemberId;
    }

    public WhetherState getShareInfo() {
        return shareInfo;
    }

    public void setShareInfo(WhetherState shareInfo) {
        this.shareInfo = shareInfo;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public String getWithholdPurpose() {
        return withholdPurpose;
    }

    public void setWithholdPurpose(String withholdPurpose) {
        this.withholdPurpose = withholdPurpose;
    }
}
