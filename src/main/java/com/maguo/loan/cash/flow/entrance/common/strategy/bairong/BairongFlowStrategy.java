package com.maguo.loan.cash.flow.entrance.common.strategy.bairong;

import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import com.maguo.loan.cash.flow.service.CacheService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
public class BairongFlowStrategy implements ProductCodeStrategy {

    public static final int TTL = 24;
    public static final String CHANNEL = "channel";
    public static final String FLOW_SOURCE = "flowSource";
    public static final String FLOW = "FLOW";
    @Autowired
    private CacheService cacheService;

    public static final String MAPPING_KEY_PREFIX = "project_mappings:code:";

    @Autowired
    public ProjectProductMappingRepository projectProductMappingRepository;



    @Override
    public boolean supports(String source, Map<String, String> params) {
        return FLOW.equalsIgnoreCase(source) &&
            FlowChannel.LTFQ.name().equalsIgnoreCase(params.get(FLOW_SOURCE));
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String channel = params.get(CHANNEL);
        if (StringUtils.isEmpty(channel)) {
            throw new IllegalArgumentException("百融流量映射必须包含 'CHANNEL' 参数");
        }
        String redisKey = MAPPING_KEY_PREFIX + channel;
        String projectCode = (String) cacheService.get(redisKey);

        if (StringUtils.isEmpty(projectCode)) {
            // 缓存中没有，从数据库查询
            Optional<ProjectProductMapping> mapping = projectProductMappingRepository.findByProductCode(channel);
            if (!mapping.isPresent()) {
                throw new IllegalArgumentException("未找到对应的产品映射配置");
            }
            projectCode = mapping.get().getProjectCode();
            // 写入缓存，并设置过期时间
            cacheService.put(redisKey, projectCode, TTL, TimeUnit.HOURS);
        }
        return projectCode;
    }
}
