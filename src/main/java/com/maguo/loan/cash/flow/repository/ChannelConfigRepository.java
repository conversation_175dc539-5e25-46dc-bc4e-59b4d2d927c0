package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.ChannelConfig;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.ChannelType;
import com.maguo.loan.cash.flow.enums.Node;
import com.maguo.loan.cash.flow.enums.WhetherState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2
 */
public interface ChannelConfigRepository extends JpaRepository<ChannelConfig, String> {

    List<ChannelConfig> findByNodeAndChannelTypeAndUseState(Node node, ChannelType channelType, WhetherState whetherState);

    ChannelConfig findByApplyChannelAndNodeAndChannelTypeAndUseState(ApplyChannel applyChannel, Node node, ChannelType channelType, WhetherState whetherState);

    ChannelConfig findByApplyChannelAndChannelTypeAndUseState(ApplyChannel applyChannel, ChannelType channelType, WhetherState whetherState);
}
