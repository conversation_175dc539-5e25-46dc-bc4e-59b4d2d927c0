package com.maguo.loan.cash.flow.entity;

import com.jinghang.capital.api.dto.BankChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * 协议展示表
 */
@Entity
@Table(name = "agreement_show")
public class AgreementShow extends BaseEntity {
    /**
     * 资金渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;

    /**
     * 模板名称
     */
    private String agreementName;

    /**
     * 协议编号
     */
    private String agreementNo;
    /**
     * 协议链接
     */
    private String agreementUrl;

    @Override
    protected String prefix() {
        return "AS";
    }


    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getAgreementName() {
        return agreementName;
    }

    public void setAgreementName(String agreementName) {
        this.agreementName = agreementName;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getAgreementUrl() {
        return agreementUrl;
    }

    public void setAgreementUrl(String agreementUrl) {
        this.agreementUrl = agreementUrl;
    }
}
