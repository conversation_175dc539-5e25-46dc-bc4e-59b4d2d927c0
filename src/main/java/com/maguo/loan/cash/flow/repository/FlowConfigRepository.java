package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.FlowConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * 流量配置
 */
public interface FlowConfigRepository extends JpaRepository<FlowConfig, String> {


    Optional<FlowConfig> findByFlowChannel(FlowChannel flowChannel);
}
