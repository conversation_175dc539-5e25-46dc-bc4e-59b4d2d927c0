package com.maguo.loan.cash.flow.entrance.ppd.dto.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

public class LoanQueryRequest {

    @NotEmpty(message = "放款流水号不能为空")
    private String loanReqNo;

    @NotBlank(message = "请求方代码不能为空")
    private String sourceCode;

    // Getters and Setters
    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }
}
