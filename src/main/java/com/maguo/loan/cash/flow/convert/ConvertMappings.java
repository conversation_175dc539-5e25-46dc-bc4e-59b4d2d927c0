package com.maguo.loan.cash.flow.convert;

import com.jinghang.common.util.StringUtil;
import org.mapstruct.Named;

public interface ConvertMappings {

    @Named("toCompanyName")
    static String toCompanyName(String companyName) {
        if (StringUtil.isBlank(companyName) || StringUtil.equals(companyName, "未知")) {
            return "未知";
        }
        if (StringUtil.equals(companyName, "其他")) {
            return "其他";
        }
        return companyName;
    }
}
