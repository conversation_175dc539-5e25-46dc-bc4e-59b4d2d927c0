package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.QualityCustomer;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "collision_record")
public class CollisionRecord extends BaseEntity {

    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    private String phone;
    /**
     * 哈希
     */
    private String certNo;
    /**
     * 哈希
     */
    private String name;

    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    private AuditState state;
    /**
     * 拒绝原因
     */
    private String failReason;

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public AuditState getState() {
        return state;
    }

    public void setState(AuditState state) {
        this.state = state;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }
}
