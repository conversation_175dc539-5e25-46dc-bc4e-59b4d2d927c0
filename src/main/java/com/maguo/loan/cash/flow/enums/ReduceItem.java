package com.maguo.loan.cash.flow.enums;

import com.maguo.loan.cash.flow.entity.OfflineRepayApply;

import java.math.BigDecimal;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 减免项
 * <AUTHOR>
 */
public enum ReduceItem {
    /**
     * 咨询费
     */
    CONSULT_FEE("咨询费",
            OfflineRepayApply::getConsultFee,
            OfflineRepayApply::setActConsultFee),
    PENALTY("罚息",
            OfflineRepayApply::getPenaltyAmt,
            OfflineRepayApply::setActPenaltyAmt),
    GUARANTEE("担保费",
            OfflineRepayApply::getGuaranteeAmt,
            OfflineRepayApply::setActGuaranteeAmt),
    INTEREST("利息",
            OfflineRepayApply::getInterestAmt,
            OfflineRepayApply::setActInterestAmt),
    PRINCIPAL("本金",
            OfflineRepayApply::getPrincipalAmt,
            OfflineRepayApply::setActPrincipalAmt);

    private final String name;
    private final Function<OfflineRepayApply, BigDecimal> getter;
    private final BiConsumer<OfflineRepayApply, BigDecimal> setter;

    ReduceItem(String name,
               Function<OfflineRepayApply, BigDecimal> getter,
               BiConsumer<OfflineRepayApply, BigDecimal> setter) {
        this.name = name;
        this.getter = getter;
        this.setter = setter;
    }

    public String getName() {
        return name;
    }

    public Function<OfflineRepayApply, BigDecimal> getGetter() {
        return getter;
    }

    public BiConsumer<OfflineRepayApply, BigDecimal> getSetter() {
        return setter;
    }
}
