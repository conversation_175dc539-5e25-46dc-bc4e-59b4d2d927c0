package com.maguo.loan.cash.flow.entrance.lvxin.dto.credit;

/**
 * @ClassName DeviceInfo
 * <AUTHOR>
 * @Description 设备信息
 * @Date 2024/5/21 17:34
 * @Version v1.0
 **/
public class DeviceInfo {
    private String gpsLng;
    private String gpsLat;
    private String gpsAddress;
    private String gpsProvince;
    private String gpsCity;
    private String osType;
    private String userIP;
    private String deviceId;
    private String simId;
    private String netStatus;
    private String systemVersion;
    private String deviceModel;

    public String getGpsLng() {
        return gpsLng;
    }

    public void setGpsLng(String gpsLng) {
        this.gpsLng = gpsLng;
    }

    public String getGpsLat() {
        return gpsLat;
    }

    public void setGpsLat(String gpsLat) {
        this.gpsLat = gpsLat;
    }

    public String getGpsAddress() {
        return gpsAddress;
    }

    public void setGpsAddress(String gpsAddress) {
        this.gpsAddress = gpsAddress;
    }

    public String getGpsProvince() {
        return gpsProvince;
    }

    public void setGpsProvince(String gpsProvince) {
        this.gpsProvince = gpsProvince;
    }

    public String getGpsCity() {
        return gpsCity;
    }

    public void setGpsCity(String gpsCity) {
        this.gpsCity = gpsCity;
    }

    public String getOsType() {
        return osType;
    }

    public void setOsType(String osType) {
        this.osType = osType;
    }

    public String getUserIP() {
        return userIP;
    }

    public void setUserIP(String userIP) {
        this.userIP = userIP;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSimId() {
        return simId;
    }

    public void setSimId(String simId) {
        this.simId = simId;
    }

    public String getNetStatus() {
        return netStatus;
    }

    public void setNetStatus(String netStatus) {
        this.netStatus = netStatus;
    }

    public String getSystemVersion() {
        return systemVersion;
    }

    public void setSystemVersion(String systemVersion) {
        this.systemVersion = systemVersion;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }
}
