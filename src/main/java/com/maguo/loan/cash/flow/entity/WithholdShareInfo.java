package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "withhold_share_info")
public class WithholdShareInfo extends BaseEntity {
    private String withholdId;
    /**
     * 商户号
     */
    private String merchantNo;
    /**
     * 商户名称
     */
    private String merchantName;
    /**
     * 本金
     */
    private BigDecimal principal;
    /**
     * 利息
     */
    private BigDecimal interest;
    /**
     * 违约金
     */
    private BigDecimal breach;
    /**
     * 罚息
     */
    private BigDecimal penalty;
    /**
     * 融担费
     */
    private BigDecimal guarantee;
    /**
     * 咨询费
     */
    private BigDecimal consult;
    /**
     * 分账总额
     */
    private BigDecimal amount;
    /**
     * 是否需要通知确认
     */
    @Enumerated(EnumType.STRING)
    private WhetherState merchantConfirm;

    public WhetherState getMerchantConfirm() {
        return merchantConfirm;
    }

    public void setMerchantConfirm(WhetherState merchantConfirm) {
        this.merchantConfirm = merchantConfirm;
    }

    public String getWithholdId() {
        return withholdId;
    }

    public void setWithholdId(String withholdId) {
        this.withholdId = withholdId;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getBreach() {
        return breach;
    }

    public void setBreach(BigDecimal breach) {
        this.breach = breach;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getGuarantee() {
        return guarantee;
    }

    public void setGuarantee(BigDecimal guarantee) {
        this.guarantee = guarantee;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getConsult() {
        return consult;
    }

    public void setConsult(BigDecimal consult) {
        this.consult = consult;
    }

    @Override
    protected String prefix() {
        return "SI";
    }
}
