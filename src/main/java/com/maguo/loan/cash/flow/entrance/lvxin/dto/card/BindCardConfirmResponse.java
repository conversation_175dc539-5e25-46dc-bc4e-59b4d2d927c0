package com.maguo.loan.cash.flow.entrance.lvxin.dto.card;

/**
 * @ClassName BindCardConfirmResponse
 * <AUTHOR>
 * @Description 绑卡确认 响应
 * @Date 2024/5/20 17:37
 * @Version v1.0
 **/
public class BindCardConfirmResponse {
    /**
     * 是否需要继续发送验证码 0-不需要  1-需要
     */
    private Integer needSmsCode;
    /**
     * 绑卡流水号
     */
    private String serialNumber;
    /**
     * 绑卡ID
     */
    private String partnerCardId;

    public Integer getNeedSmsCode() {
        return needSmsCode;
    }

    public void setNeedSmsCode(Integer needSmsCode) {
        this.needSmsCode = needSmsCode;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getPartnerCardId() {
        return partnerCardId;
    }

    public void setPartnerCardId(String partnerCardId) {
        this.partnerCardId = partnerCardId;
    }
}
