package com.maguo.loan.cash.flow.entrance.lvxin.dto.loan;

import java.math.BigDecimal;

/**
 * @ClassName LoanTrialRequest
 * <AUTHOR>
 * @Description 借款试算 请求参数
 * @Date 2024/5/21 11:04
 * @Version v1.0
 **/
public class LoanTrialRequest {
    private String userId;
    private String partnerUserId;
    /**
     * 借款金额 单位:元
     */
    private BigDecimal loanAmount;
    private Integer period;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPartnerUserId() {
        return partnerUserId;
    }

    public void setPartnerUserId(String partnerUserId) {
        this.partnerUserId = partnerUserId;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }
}
