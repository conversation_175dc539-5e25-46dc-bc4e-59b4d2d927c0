package com.maguo.loan.cash.flow.entrance.lvxin.dto.user;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName QuotaQueryResponse
 * <AUTHOR>
 * @Description 用户授信额度查询 响应
 * @Date 2024/5/17 15:21
 * @Version v1.0
 **/
public class QuotaQueryResponse {
    /**
     * 授信状态 0审核中，1通过，2拒绝
     */
    private Integer status;
    /**
     * 拒绝理由 - 拒绝时必填
     */
    private String reason;
    /**
     * 授信金额 - 通过时必填 - 单位 元
     */
    private BigDecimal creditAmount;
    /**
     * 可借金额 - 通过时必填 - 单位 元
     */
    private BigDecimal canBorrowAmount;
    /**
     * 可借期数 - 通过时必填
     */
    private List<Integer> terms;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public BigDecimal getCanBorrowAmount() {
        return canBorrowAmount;
    }

    public void setCanBorrowAmount(BigDecimal canBorrowAmount) {
        this.canBorrowAmount = canBorrowAmount;
    }

    public List<Integer> getTerms() {
        return terms;
    }

    public void setTerms(List<Integer> terms) {
        this.terms = terms;
    }
}
