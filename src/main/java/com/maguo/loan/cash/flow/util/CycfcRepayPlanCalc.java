package com.maguo.loan.cash.flow.util;


import com.jinghang.common.loan.PlanGenerator;
import com.jinghang.common.loan.plan.InterestType;
import com.jinghang.common.loan.plan.RepayPlan;
import com.maguo.loan.cash.flow.enums.QhBank;
import com.maguo.loan.cash.flow.enums.RateLevel;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 润楼长银
 *
 * <AUTHOR>
 */
public class CycfcRepayPlanCalc {

    public static final BigDecimal SAN_LIU_LING = new BigDecimal("360");

    public static final Integer TEN = 10;


    public static List<RepayPlanItem> calcConsultPlan(BigDecimal loanAmt, int periods) {
        List<RepayPlanItem> repayPlanItems = calcGuaranteePlan(loanAmt, periods);
        // 平台对客IRR36,等额本息计算月供
        List<RepayPlan> consultPlans = calcBasicPlan(loanAmt, RateLevel.RATE_36.getRate(), periods);
        for (RepayPlanItem planItem : repayPlanItems) {
            // 计算咨询费
            RepayPlan consultPlan = consultPlans.stream().filter(c -> c.getCurrentPeriod() == planItem.getPeriod()).findFirst().orElseThrow();
            planItem.setConsultAmt(consultPlan.getPrincipal().add(consultPlan.getInterest())
                .subtract(planItem.getCapitalTotalAmt()));
            // 平台对客 = 资方对客 + 咨询费
            planItem.setCustomTotalAmt(planItem.getCapitalTotalAmt().add(planItem.getConsultAmt()));
        }

        return repayPlanItems;
    }


    /**
     * 融担费还款计划  融担费日利率（ 融担年化/365 ） * 占用天数 * 剩余本金
     * 按日计算  例如：【23.89%（资方对客利率）- 8.4%（资方利息利率）】/ 365(天)  * 占用天数  * 当前期剩余本金
     *
     * @param loanAmt
     * @param periods
     * @return
     */
    public static List<RepayPlanItem> calcGuaranteePlan(BigDecimal loanAmt, int periods) {
        List<RepayPlanItem> repayPlanItems = new ArrayList<>();
        //bank合同利率
        List<RepayPlan> bankRateRepayPlans = calcBasicPlan(loanAmt, QhBank.CYBK.getBankRate(), periods);
        //bank对客利率
        List<RepayPlan> bankCustomRateRepayPlans = calcBasicPlan(loanAmt, QhBank.CYBK.getBankCustomRate(), periods);


        for (int i = 0; i < bankRateRepayPlans.size(); i++) {
            RepayPlan plan = bankRateRepayPlans.get(i);
            RepayPlanItem repayPlanItem = new RepayPlanItem();
            repayPlanItem.setPeriod(plan.getCurrentPeriod());
            repayPlanItem.setPrincipalAmt(plan.getPrincipal());
            repayPlanItem.setInterestAmt(plan.getInterest());
            // 计算融担费
            RepayPlan bankCustomerRateRepayPlan = bankCustomRateRepayPlans.get(i);
            repayPlanItem.setGuaranteeAmt(bankCustomerRateRepayPlan.getPrincipal().add(bankCustomerRateRepayPlan.getInterest())
                .subtract(plan.getPrincipal().add(plan.getInterest())));
            // 总金额
            repayPlanItem.setCapitalTotalAmt(repayPlanItem.getPrincipalAmt().add(repayPlanItem.getInterestAmt()).add(repayPlanItem.getGuaranteeAmt()));
            repayPlanItems.add(repayPlanItem);
        }
        return repayPlanItems;
    }

    /**
     * 等额本息
     *
     * @param loanAmt
     * @param interestRate
     * @param periods
     * @return
     */
    private static List<RepayPlan> calcBasicPlan(BigDecimal loanAmt, BigDecimal interestRate, int periods) {
        return PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, loanAmt, interestRate, periods);
    }

}
