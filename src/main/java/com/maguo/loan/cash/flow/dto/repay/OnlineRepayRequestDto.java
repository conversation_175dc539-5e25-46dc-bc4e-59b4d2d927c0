package com.maguo.loan.cash.flow.dto.repay;


import com.maguo.loan.cash.flow.enums.OperationSource;
import com.maguo.loan.cash.flow.enums.RepayPurpose;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class OnlineRepayRequestDto {

    private String loanId;

    private Integer period;

    private RepayPurpose repayPurpose;

    private String outerRepayNo;

    private OperationSource operationSource;

    //咨询费减免
    private BigDecimal consultationFeeWaiver;

    //罚息减免
    private BigDecimal penaltyInterestWaiver;

    //总金额
    private BigDecimal repayTotalAmount;
    private String repayDate ;

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public String getOuterRepayNo() {
        return outerRepayNo;
    }

    public void setOuterRepayNo(String outerRepayNo) {
        this.outerRepayNo = outerRepayNo;
    }

    public OperationSource getOperationSource() {
        return operationSource;
    }

    public void setOperationSource(OperationSource operationSource) {
        this.operationSource = operationSource;
    }

    public BigDecimal getConsultationFeeWaiver() {
        return consultationFeeWaiver;
    }

    public void setConsultationFeeWaiver(BigDecimal consultationFeeWaiver) {
        this.consultationFeeWaiver = consultationFeeWaiver;
    }

    public BigDecimal getPenaltyInterestWaiver() {
        return penaltyInterestWaiver;
    }

    public void setPenaltyInterestWaiver(BigDecimal penaltyInterestWaiver) {
        this.penaltyInterestWaiver = penaltyInterestWaiver;
    }

    public BigDecimal getRepayTotalAmount() {
        return repayTotalAmount;
    }

    public void setRepayTotalAmount(BigDecimal repayTotalAmount) {
        this.repayTotalAmount = repayTotalAmount;
    }
}
