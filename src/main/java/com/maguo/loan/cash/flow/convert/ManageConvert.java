package com.maguo.loan.cash.flow.convert;

import com.google.inject.name.Named;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.ppd.api.dto.RepayTrailDto;
import com.jinghang.ppd.api.dto.file.FileDownloadRespDTO;
import com.jinghang.ppd.api.dto.order.OrderQueryResponseDto;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import com.maguo.loan.cash.flow.dto.OfflineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.OfflineRepayApply;
import com.maguo.loan.cash.flow.entity.OfflineRepayReduce;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.vo.RepayTrialRequest;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.ValueMapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Mapper(imports = {LocalDateTime.class, ProcessState.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ManageConvert {

    ManageConvert INSTANCE = Mappers.getMapper(ManageConvert.class);


    com.jinghang.ppd.api.dto.TrailResultDto toTrailResultDto(TrialResultVo trailDto);

    @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.THROW_EXCEPTION)
    @Mapping(source = "amount", target = "actAmount")
    OfflineRepayApplyRequest toRepayApplyRequest(RepayApplyDto trailDto);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "applyTime", expression = "java( LocalDateTime.now() )")
    @Mapping(target = "applyState", expression = "java( ProcessState.PROCESSING )")
    OfflineRepayApply toOfflineRepayApply(OfflineRepayReduce repayReduce);

    @Mapping(target = "applyTime", expression = "java( LocalDateTime.now() )")
    @Mapping(target = "applyState", expression = "java( ProcessState.PROCESSING )")
    @Mapping(source = "principal", target = "principalAmt")
    @Mapping(source = "interest", target = "interestAmt")
    @Mapping(source = "penalty", target = "penaltyAmt")
    @Mapping(source = "guaranteeFee", target = "guaranteeAmt")
    @Mapping(source = "consultFee", target = "consultFee")
    @Mapping(source = "amount", target = "amount")
    @Mapping(target = "reduceAmount", constant = "0")
    @Mapping(source = "principal", target = "actPrincipalAmt")
    @Mapping(source = "interest", target = "actInterestAmt")
    @Mapping(source = "penalty", target = "actPenaltyAmt")
    @Mapping(source = "guaranteeFee", target = "actGuaranteeAmt")
    @Mapping(source = "consultFee", target = "actConsultFee")
    @Mapping(source = "amount", target = "actAmount")
    OfflineRepayApply toOfflineRepayApply(TrialResultVo trialResultVo);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "id", target = "outerRepayNo")
    @Mapping(target = "repayApplyDate", expression = "java( LocalDateTime.now() )")
    @Mapping(target = "operationSource", constant = "CUSTOM_SERVICE")
    @Mapping(target = "repayMode", constant = "OFFLINE")
    @Mapping(source = "actPrincipalAmt", target = "principalAmt")
    @Mapping(source = "actInterestAmt", target = "interestAmt")
    @Mapping(source = "actGuaranteeAmt", target = "guaranteeAmt")
    @Mapping(source = "actPenaltyAmt", target = "penaltyAmt")
    @Mapping(source = "actConsultFee", target = "consultFee")
    @Mapping(source = "actAmount", target = "totalAmt")
    @Mapping(source = "reduceAmount", target = "reduceAmount")
    @Mapping(target = "repayState", constant = "INIT")
    @Mapping(target = "needTwiceState", constant = "N")
    @Mapping(target = "breachAmt", source = "breachFee")
    CustomRepayRecord toCustomRepayRecord(OfflineRepayApply repayApply);

    FileDownloadRespDTO toFileDownloadResp(FileDownloadResultDto finResultDto);

    @Mapping(source = "period", target = "periods")
    @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.THROW_EXCEPTION)
    RepayTrialRequest toRepayTrialRequest(RepayTrailDto trailDto);

    @Mapping(source = "id", target = "orderId")
    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "outerOrderId", target = "outerOrderId")
    @Mapping(source = "openId", target = "openId")
    @Mapping(source = "riskId", target = "riskId")
    @Mapping(source = "flowChannel", target = "flowChannel")
    @Mapping(source = "applyChannel", target = "applyChannel")
    @Mapping(source = "applicationSource", target = "applicationSource")
    @Mapping(source = "orderState", target = "orderState")
    @Mapping(source = "applyTime", target = "applyTime")
    @Mapping(source = "loanTime", target = "loanTime")
    @Mapping(source = "applyAmount", target = "applyAmount")
    @Mapping(source = "approveAmount", target = "approveAmount")
    @Mapping(source = "monthPay", target = "monthPay")
    @Mapping(source = "irrRate", target = "irrRate")
    @Mapping(source = "applyPeriods", target = "applyPeriods")
    @Mapping(source = "amountType", target = "amountType")
    @Mapping(source = "loanPurpose", target = "loanPurpose")
    @Mapping(source = "renewedFlag", target = "renewedFlag")
    @Mapping(source = "bankChannel", target = "bankChannel")
    @Mapping(source = "loanCardId", target = "loanCardId")
    @Mapping(source = "rightsPackageId", target = "rightsPackageId")
    @Mapping(source = "bindCapitalCardState", target = "bindCapitalCardState")
    @Mapping(source = "orderSubmitState", target = "orderSubmitState")
    @Mapping(source = "name", target = "name")
    @Mapping(source = "mobile", target = "mobile")
    @Mapping(source = "certNo", target = "certNo")
    @Mapping(source = "approveRights", target = "approveRights")
    @Mapping(source = "approveRightsForce", target = "approveRightsForce")
    @Mapping(source = "rightsMarking", target = "rightsMarking")
    @Mapping(source = "approveRate", target = "approveRate")
    OrderQueryResponseDto toOrderQueryResponseDto(Order order);

    @Named("rateLevelToBigDecimal")
    default BigDecimal rateLevelToBigDecimal(RateLevel level) {
        if (level == null) {
            return null;
        }
        return new BigDecimal(level.ordinal());
    }

    @Named("whetherStateToBigDecimal")
    default BigDecimal whetherStateToBigDecimal(WhetherState state) {
        if (state == null) {
            return null;
        }
        return new BigDecimal(state.ordinal());
    }

    @Named("rightsLevelToBigDecimal")
    default BigDecimal rightsLevelToBigDecimal(RightsLevel level) {
        if (level == null) {
            return null;
        }
        return new BigDecimal(level.ordinal());
    }
}
