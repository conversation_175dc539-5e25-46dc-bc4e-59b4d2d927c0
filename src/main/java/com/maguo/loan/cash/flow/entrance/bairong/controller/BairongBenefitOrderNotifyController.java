package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderNotifyRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderNotifyResponse;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongBenefitOrderNotifyService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权益订单通知
 * 功能：接收百融发送的机构权益订单信息通知，处理后返回接收结果
 */
@RestController
@RequestMapping("/benefit/notify")
@RequiredArgsConstructor
public class BairongBenefitOrderNotifyController extends BairongApiValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(BairongBenefitOrderNotifyController.class);

    private final BairongBenefitOrderNotifyService bairongBenefitOrderNotifyService;

    /**
     * 权益订单通知接收接口
     * 百融向机构推送权益订单信息，同一借据可能多次通知，需覆盖更新
     *
     * @param request 权益订单通知请求参数
     * @return 接口响应结果
     */
    @PostMapping("/BRYC")
    public BairongBenefitOrderNotifyResponse receiveBenefitOrderNotify(@RequestBody BairongBenefitOrderNotifyRequest request) {
        return bairongBenefitOrderNotifyService.receiveBenefitOrderNotify(request);
    }
}
