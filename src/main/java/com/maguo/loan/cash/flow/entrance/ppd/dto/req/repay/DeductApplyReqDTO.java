package com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay;

import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.common.BaseRepayRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

/**
 * 扣款请求数据传输对象
 * <AUTHOR>
 */
public class DeductApplyReqDTO  implements BaseRepayRequest {
    /**
     * 放款请求流水号（唯一标识）
     * <p>格式示例：LOAN2023********</p>
     */
    @NotBlank(message = "放款请求流水号不能为空")
    private String loanReqNo;

    /**
     * 请求方代码
     * <p>固定值：CJCYDL_PPD2 或 CJCYDL_PPD3</p>
     */
    @NotBlank(message = "请求方代码不能为空")
    private String sourceCode;

    /**
     * 还款请求流水号（唯一标识）
     * <p>格式示例：REPAY2023********</p>
     */
    @NotBlank(message = "还款请求流水号不能为空")
    private String repayNo;

    /**
     * 还款类型代码
     * <p>01-按期正常还款 | 03-一次性提前结清 | 04-逾期整期还款</p>
     */
    @NotBlank(message = "还款类型不能为空")
    private String repayType;

    /**
     * 银行机构代码
     * <p>参见银行代码对照表，示例：ICBC（工商银行）</p>
     */
    @NotBlank(message = "银行代码不能为空")
    private String bankCode;

    /**
     * 银行卡号
     * <p>示例：6222021000001234567</p>
     */
    @NotBlank(message = "银行卡号不能为空")
    private String bankAcct;

    /**
     * 银行卡账户名
     * <p>示例：张三</p>
     */
    @NotBlank(message = "银行卡账户名不能为空")
    private String acctName;

    /**
     * 银行预留手机号
     * <p>11位数字，示例：***********</p>
     */
    @NotBlank(message = "持卡人预留手机号不能为空")
    private String bankMobile;

    /**
     * 还款时间
     * <p>格式：yyyyMMddHHmmss，示例：**************</p>
     */
    @NotBlank(message = "还款时间不能为空")
    private String repayTime;

    /**
     * 还款期次
     * <p>数值型，结清还款传起始期次，示例：6（表示第6期）</p>
     */
    @NotNull(message = "还款期次不能为空")
    private Integer repayTerm;

    /**
     * 还款总金额（单位：元）
     * <p>各费用项之和，精确到分，示例：1250.68</p>
     */
    @NotNull(message = "还款总金额不能为空")
    private BigDecimal repayAmount;

    /**
     * 还款本金（单位：元）
     * <p>精确到分，示例：1000.00</p>
     */
    @NotNull(message = "还款本金不能为空")
    private BigDecimal repayPrincipal;

    /**
     * 还款利息（单位：元）
     * <p>精确到分，示例：200.00</p>
     */
    @NotNull(message = "还款利息不能为空")
    private BigDecimal repayInterest;

    /**
     * 还款罚息（单位：元）
     * <p>精确到分，示例：50.68</p>
     */
    @NotNull(message = "还款罚息不能为空")
    private BigDecimal repayOverdue;

    /**
     * 还款手续费（单位：元）
     * <p>精确到分，示例：0.00</p>
     */
    @NotNull(message = "还款手续费不能为空")
    private BigDecimal repayPoundage;

    /**
     * 还款提结违约金（单位：元）
     * <p>精确到分，示例：0.00</p>
     */
    @NotNull(message = "还款提结违约金不能为空")
    private BigDecimal repayLateFee;

    /**
     * 扣款渠道代码（可选）
     * <p>默认值：BAOFOOXY（宝付）<br>
     * 可选值：XYB（易宝）</p>
     */
    private String bankChannel;

    /**
     * 签约协议号（条件可选）
     * <p>当扣款渠道为BAOFOOXY时必填</p>
     */
    private String channelRepayId;

    /**
     * 主扣商户号（合并支付时使用）
     * <p>支付渠道母单扣款商户号</p>
     */
    private String mainMemberId;

    /**
     * 母单扣款流水号（合并支付时使用）
     */
    private String payOrderNo;

    @Override
    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    @Override
    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    @Override
    public String getRepayNo() {
        return repayNo;
    }

    public void setRepayNo(String repayNo) {
        this.repayNo = repayNo;
    }

    @Override
    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getBankMobile() {
        return bankMobile;
    }

    public void setBankMobile(String bankMobile) {
        this.bankMobile = bankMobile;
    }

    @Override
    public String getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(String repayTime) {
        this.repayTime = repayTime;
    }

    @Override
    public Integer getRepayTerm() {
        return repayTerm;
    }

    public void setRepayTerm(Integer repayTerm) {
        this.repayTerm = repayTerm;
    }

    @Override
    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    @Override
    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) {
        this.repayPrincipal = repayPrincipal;
    }

    @Override
    public BigDecimal getRepayInterest() {
        return repayInterest;
    }

    public void setRepayInterest(BigDecimal repayInterest) {
        this.repayInterest = repayInterest;
    }

    @Override
    public BigDecimal getRepayOverdue() {
        return repayOverdue;
    }

    public void setRepayOverdue(BigDecimal repayOverdue) {
        this.repayOverdue = repayOverdue;
    }

    @Override
    public BigDecimal getRepayPoundage() {
        return repayPoundage;
    }

    public void setRepayPoundage(BigDecimal repayPoundage) {
        this.repayPoundage = repayPoundage;
    }

    @Override
    public BigDecimal getRepayLateFee() {
        return repayLateFee;
    }

    public void setRepayLateFee(BigDecimal repayLateFee) {
        this.repayLateFee = repayLateFee;
    }

    public String getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(String bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getChannelRepayId() {
        return channelRepayId;
    }

    public void setChannelRepayId(String channelRepayId) {
        this.channelRepayId = channelRepayId;
    }

    public String getMainMemberId() {
        return mainMemberId;
    }

    public void setMainMemberId(String mainMemberId) {
        this.mainMemberId = mainMemberId;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }
}
