package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.OfflineRepayReduce;
import com.maguo.loan.cash.flow.enums.AuditStatus;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.UseState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface OfflineRepayReduceRepository extends JpaRepository<OfflineRepayReduce, String> {

    int countByOrderIdAndUseState(String orderId, UseState useState);

    OfflineRepayReduce findByLoanIdAndPeriodAndRepayPurposeAndAuditStateAndUseState(String loanId, Integer period, RepayPurpose repayPurpose,
                                                                                    AuditStatus auditStatus, UseState useState);

    OfflineRepayReduce findByLoanIdAndPeriodAndAuditStateAndUseState(String loanId, Integer period,
                                                                                    AuditStatus auditStatus, UseState useState);

    OfflineRepayReduce findByOrderIdAndPeriodAndUseState(String orderId, Integer period, UseState useState);

    List<OfflineRepayReduce> findByLoanIdAndUseState(String loanId, UseState useState);

    OfflineRepayReduce findByOrderId(String orderId);

    @Query(value = "SELECT orr.* FROM offline_repay_reduce orr " +
        "INNER JOIN loan ON orr.loan_id = loan.id " +
        "WHERE loan.flow_channel = :flowChannel " +
        "AND orr.audit_state = 'PASS' " +
        "AND orr.use_state = 'WAIT' " +
        "AND DATE(orr.created_time) = :targetDate",
        nativeQuery = true)
    List<OfflineRepayReduce> findByFlowChannelWithDate(
        @Param("flowChannel") String flowChannel,
        @Param("targetDate") LocalDate targetDate);

    OfflineRepayReduce findByOrderIdAndAuditStateAndUseState(String orderId,AuditStatus auditStatus, UseState useState);
}
