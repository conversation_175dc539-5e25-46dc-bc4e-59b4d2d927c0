package com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay;

import com.maguo.loan.cash.flow.entrance.ppd.dto.CommonResult;

import java.math.BigDecimal;

/**
 * 减免申请结果查询响应数据传输对象
 */
public class ReductionResultRespDTO extends CommonResult {
    /**
     * 实际可减免总金额(元)
     */
    private BigDecimal reductAmount;
    /**
     * 减免有效期(yyyyMMddHHmmss)
     */
    private String effectiveDate;

    public BigDecimal getReductAmount() {
        return reductAmount;
    }

    public void setReductAmount(BigDecimal reductAmount) {
        this.reductAmount = reductAmount;
    }

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }
}
