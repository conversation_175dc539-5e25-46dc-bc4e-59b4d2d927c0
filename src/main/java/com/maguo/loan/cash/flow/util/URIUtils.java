package com.maguo.loan.cash.flow.util;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Objects;

public class URIUtils {

    public static String decodeURIComponent(String uriComponent) {
        Objects.requireNonNull(uriComponent);
        // 使用UTF-8进行解码
        try {
            return URLDecoder.decode(uriComponent, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // 这个异常在实际中不应该发生，因为"UTF-8"是标准的字符编码
            throw new AssertionError("UTF-8 is not supported?", e);
        }
    }

    public static String encodeURIComponent(String uriComponent) {
        Objects.requireNonNull(uriComponent);
        // 使用UTF-8进行解码
        try {
            return URLEncoder.encode(uriComponent, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // 这个异常在实际中不应该发生，因为"UTF-8"是标准的字符编码
            throw new AssertionError("UTF-8 is not supported?", e);
        }
    }
}
