package com.maguo.loan.cash.flow.entrance.lvxin.controller;


import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.enums.CapitalRoute;
import com.maguo.loan.cash.flow.annotation.LogRequest;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProjectCodeMapper;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ApprovalRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ApprovalResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.CreditQueryRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.CreditQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.service.LvxinService;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.service.CreditValidationService;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.util.AgeUtil;
import com.maguo.loan.cash.flow.util.BaseConstants;
import com.maguo.loan.cash.flow.util.RedisKeyConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("lvxin")
public class LvxinCreditController {

    private static final Logger logger = LoggerFactory.getLogger(LvxinCreditController.class);

    @Autowired
    private LvxinService lvxinService;

    @Autowired
    private LockService lockService;

    @Autowired
    private CreditValidationService creditValidationService;

    @Autowired
    private ProjectCodeMapper projectCodeMapper;

    @Autowired
    private ProjectInfoService projectInfoService;


    /**
     * 授信申请
     */
    @LogRequest(flowIdentifier = "LVXIN")
    @PostMapping({"/api/partner/v1/apply", "/api/partner/v2/apply"})
    public LvxinResponse creditApply(@RequestBody ApprovalRequest approvalRequest) {
        Locker lock = lockService.getLock(RedisKeyConstants.APPROVAL_APPLY + approvalRequest.getIdCard());
        ApprovalResponse approval = new ApprovalResponse();
        try {
            boolean locked = lock.tryLock(Duration.ZERO, BaseConstants.DEFAULT_LOCK_RELEASE_TIME);
            if (locked) {
                // 公共参数校验
                String validateMessage = validateCommonParameters(approvalRequest);

                if (StringUtils.isNotBlank(validateMessage)) {
                    logger.warn("授信申请 pre-check 失败, 原因: {}", validateMessage);
                    return LvxinResponse.fail(validateMessage);
                }

                approval = lvxinService.approval(approvalRequest);
            } else {
                throw new BizException(ResultCode.APPROVAL_APPLY_REPEAT);
            }
        } catch (InterruptedException e) {
            //ignore
        } finally {
            lock.unlock();
        }
        return LvxinResponse.success(approval);
    }

    /**
     * 公共参数校验
     */
    private String validateCommonParameters(ApprovalRequest request) {
        String projectCode = getProjectCode(request);
        ProjectInfoDto projectElements = projectInfoService.queryProjectInfo(projectCode);
        if (projectElements == null){
            return "未配置项目信息";
        }
        if (StringUtils.isNotBlank(projectCode)) {
            String capitalRoute = projectElements.getElements().getCapitalRoute().getCode();
            //判断资方路由是直连还是路由
            if (Objects.equals(capitalRoute, CapitalRoute.DIRECT.getCode())) {
                // 校验授信黑暗期
                if (creditValidationService.isInCreditDarkHours(projectElements)) {
                    return "当前时间在授信黑暗期内，请稍后再试";
                }
            }

            // 校验日授信限额
            if (creditValidationService.isDailyCreditLimitExceeded(projectElements, request.getCreditAmount())) {
                return "日授信限额超限";
            }
            Integer calculateAge = AgeUtil.calculateAge(request.getIdCard());
            // 校验年龄范围
            if (!creditValidationService.isAgeInRange(projectElements, calculateAge)) {
                return "年龄不在允许范围内";
            }

            // 校验可提现范围
            if (!creditValidationService.isAmountInDrawableRange(projectElements, request.getCreditAmount())) {
                return "申请金额不在可提现范围内";
            }

        }

        return null;
    }

    /**
     * 查询授信结果
     */
    @PostMapping({"/api/partner/v1/queryApplyResult","/api/partner/v2/queryApplyResult"})
    public LvxinResponse creditResultQuery(@RequestBody CreditQueryRequest creditQueryRequest) {
        CreditQueryResponse response = lvxinService.creditResultQuery(creditQueryRequest);
        return LvxinResponse.success(response);
    }

    /**
     * 获取项目编码
     */
    private String getProjectCode(ApprovalRequest request) {
        String productType = request.getProductType();
        Map<String, String> params = new HashMap<>();
        params.put("flowSource", FlowChannel.LVXIN.name());
        params.put("productType", productType);
        String projectCode = projectCodeMapper.getProjectCode("FLOW", params);

        return projectCode;
    }
}
