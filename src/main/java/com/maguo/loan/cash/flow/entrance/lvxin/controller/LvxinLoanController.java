package com.maguo.loan.cash.flow.entrance.lvxin.controller;


import com.maguo.loan.cash.flow.annotation.LogLoanRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LoanTrialRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LoanTrialResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinLoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinLoanApplyResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinLoanQueryRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinLoanQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinRepayPlanQueryRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinRepayPlanQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.service.LvxinService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("lvxin")
public class LvxinLoanController {

    @Autowired
    private LvxinService lvxinService;


    /**
     * 借款试算
     *
     * @param request
     * @return
     */
    @PostMapping({"/api/partner/v1/calculator","/api/partner/v2/calculator"})
    public LvxinResponse loanTrial(@RequestBody LoanTrialRequest request) {
        LoanTrialResponse loanTrialResponse = lvxinService.loanTrial(request);
        return LvxinResponse.success(loanTrialResponse);
    }

    /**
     * 借款申请
     *
     * @param request
     * @return
     */
    @PostMapping({"/api/partner/v1/confirmLoan","/api/partner/v2/confirmLoan"})
    @LogLoanRequest(flowIdentifier = "LVXIN")
    public LvxinResponse confirmLoan(@RequestBody @Valid LvxinLoanApplyRequest request) {
        LvxinLoanApplyResponse response = lvxinService.loanApply(request);
        return LvxinResponse.success(response);
    }

    /**
     * 放款结果查询
     *
     * @param request
     * @return
     */
    @PostMapping({"/api/partner/v1/queryLoanResult","/api/partner/v2/queryLoanResult"})
    public LvxinResponse queryLoanResult(@RequestBody LvxinLoanQueryRequest request) {
        LvxinLoanQueryResponse response = lvxinService.queryLoanResult(request);
        return LvxinResponse.success(response);
    }

    /**
     * 还款计划查询
     *
     * @param request
     * @return
     */
    @PostMapping({"/api/partner/v1/queryRepaymentPlan","/api/partner/v2/queryRepaymentPlan"})
    public LvxinResponse queryLoanResult(@RequestBody LvxinRepayPlanQueryRequest request) {
        LvxinRepayPlanQueryResponse response = lvxinService.queryRepayPlan(request.getPartnerOrderNo());
        return LvxinResponse.success(response);
    }
}
