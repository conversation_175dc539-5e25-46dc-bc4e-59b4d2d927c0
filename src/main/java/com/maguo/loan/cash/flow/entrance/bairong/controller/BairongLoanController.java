package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.enums.CapitalRoute;
import com.maguo.loan.cash.flow.annotation.LogRequest;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.bairong.constans.BairongMessageConstants;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongCreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongLoanApplyResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongLoanQueryRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongLoanQueryResponse;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongLoanStatus;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongService;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProjectCodeMapper;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.service.CreditValidationService;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.util.AgeUtil;
import com.maguo.loan.cash.flow.util.BaseConstants;
import com.maguo.loan.cash.flow.util.RedisKeyConstants;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 百融放款接口
 * @Author: hehangzheng
 * @Date: 2025-9-10 下午 02:35
 */
@RestController
@RequestMapping("bairong")
public class BairongLoanController extends BairongApiValidator {

    private static final Logger logger = LoggerFactory.getLogger(BairongLoanController.class);
    public static final String FLOW_SOURCE = "flowSource";
    public static final String PRODUCT_TYPE = "productType";
    public static final String FLOW = "FLOW";

    @Autowired
    private BairongService bairongService;
    @Autowired
    private LockService lockService;
    @Autowired
    private ProjectCodeMapper projectCodeMapper;
    @Autowired
    private CreditValidationService creditValidationService;
    @Autowired
    private ProjectInfoService projectInfoService;

    /**
     * 百融统一贷款申请入口 (融合授信与放款)
     */
    @PostMapping("/loan/apply/BRYC")
    @LogRequest(flowIdentifier = "LTFQ")
    public BairongLoanApplyResponse loanApply(@RequestBody @Valid BairongCreditApplyRequest request) {
        Locker lock = lockService.getLock(RedisKeyConstants.APPROVAL_APPLY + request.getLoanInfo().getOutLoanSeq());
        BairongLoanApplyResponse approval = new BairongLoanApplyResponse();

        try {
            boolean locked = lock.tryLock(Duration.ZERO, BaseConstants.DEFAULT_LOCK_RELEASE_TIME);
            if (locked) {
                // 公共参数校验
                String validateMessage = validateBasicParameters(request);
                if (StringUtils.isNotBlank(validateMessage)) {
                    logger.warn("授信申请 pre-check 失败, 原因: {}", validateMessage);
                    approval.setDnSts(BairongLoanStatus.LOAN_FAILED.getCode());
                    approval.setPayMsg(validateMessage);
                    return approval;
                }

                return bairongService.approval(request);
            } else {
                throw new BizException(ResultCode.APPROVAL_APPLY_REPEAT);
            }
        } catch (InterruptedException e) {
            //ignore
        } finally {
            lock.unlock();
        }
        return null;
    }

    /**
     * 基础参数校验
     */
    private String validateBasicParameters(BairongCreditApplyRequest request) {
        String projectCode = getProjectCode(request);
        ProjectInfoDto projectElements = projectInfoService.queryProjectInfo(projectCode);
        if (projectElements == null) {
            return BairongMessageConstants.PROJECT_CONFIG_NOT_FOUND;
        }
        if (StringUtils.isNotBlank(projectCode)) {
            String capitalRoute = projectElements.getElements().getCapitalRoute().getCode();
            //判断资方路由是直连还是路由
            if (Objects.equals(capitalRoute, CapitalRoute.DIRECT.getCode())) {
                // 校验授信黑暗期
                if (creditValidationService.isInCreditDarkHours(projectElements)) {
                    return BairongMessageConstants.CREDIT_DARK_HOURS;
                }
            }

            // 校验日授信限额
            if (creditValidationService.isDailyCreditLimitExceeded(projectElements, request.getLoanInfo().getDnAmt())) {
                return BairongMessageConstants.DAILY_LIMIT_EXCEEDED;
            }
            Integer calculateAge = AgeUtil.calculateAge(request.getBasicInfo().getIdNo());
            // 校验年龄范围
            if (!creditValidationService.isAgeInRange(projectElements, calculateAge)) {
                return BairongMessageConstants.AGE_NOT_IN_RANGE;
            }

            // 校验可提现范围
            if (!creditValidationService.isAmountInDrawableRange(projectElements, request.getLoanInfo().getDnAmt())) {
                return BairongMessageConstants.AMOUNT_NOT_IN_DRAWABLE_RANGE;
            }

            //校验影像件是否存在
            if (!creditValidationService.validateImageFilesExist(request.getLoanInfo().getOutLoanSeq())) {
                return BairongMessageConstants.IMAGE_FILES_EXIST;
            }
        }
        return null;
    }

    /**
     * 获取项目编码
     */
    private String getProjectCode(BairongCreditApplyRequest request) {
        String productType = request.getChannel();
        Map<String, String> params = new HashMap<>();
        params.put(FLOW_SOURCE, FlowChannel.LTFQ.name());
        params.put(PRODUCT_TYPE, productType);
        return projectCodeMapper.getProjectCode(FLOW, params);
    }

    /**
     * 放款状态查询
     *
     * @param request 请求参数
     * @return 返回结果
     */
    @PostMapping("/loan/query/BRYC")
    public BairongLoanQueryResponse queryLoanResult(@RequestBody @Valid BairongLoanQueryRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            // 业务逻辑
            return bairongService.queryLoanResult(request);
        } catch (Exception e) {
            logger.error("百融放款状态查询失败", e);
            return BairongLoanQueryResponse.failure(request.getOutLoanSeq(), request.getLoanSeq(), request.getApplCde(),
                BairongLoanStatus.LOAN_ABNORMAL.getCode(), null, BairongLoanStatus.LOAN_ABNORMAL.getDesc());
        }
    }
}
