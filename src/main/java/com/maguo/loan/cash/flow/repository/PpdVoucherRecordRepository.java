package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.ppd.PpdVoucherRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface PpdVoucherRecordRepository extends JpaRepository<PpdVoucherRecord, String>, JpaSpecificationExecutor<PpdVoucherRecord> {

    PpdVoucherRecord findByLoanId(String loanId);
}
