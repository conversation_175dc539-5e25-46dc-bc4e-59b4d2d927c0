package com.maguo.loan.cash.flow.config;

import com.jinghang.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

/**
 * 提供绿信新sftp配置
 * @作者 Mr.sandman
 * @时间 2025/07/16 11:37
 */
@Configuration
public class LvXinNewSFTPConfig {

    /**
     * 绿信新sftp用户名
     */
    @Value("${lvxin.new.sftp.username}")
    private String sftpUserName;
    /**
     * 绿信新sftp密码
     */
    @Value("${lvxin.new.sftp.password}")
    private String sftpPassword;
    /**
     * 绿信新sftp地址
     */
    @Value("${lvxin.new.sftp.ip}")
    private String sftpIp;
    /**
     * 绿信新sftp端口
     */
    @Value("${lvxin.new.sftp.port}")
    private Integer sftpPort;
    /**
     * 绿信新签章sftpPath
     */
    @Value("${lvxin.new.agreement.sftpPath}")
    private String agreementSftpPath;
    //权益签章
    @Value("${lvxin.new.IncludingEquity.agreement.sftpPath}")
    private String includingEquityAgreementSftpPath;
    /**
     * 绿信新结清证明sftpPath
     */
    @Value("${lvxin.new.clearVoucher.sftpPath}")
    private String clearVoucherSftpPath;
    /**
     * 绿信权益结清证明sftpPath
     */
    @Value("${lvxin.new.IncludingEquity.clearVoucher.sftpPath}")
    private String IncludingEquityClearVoucherSftpPath;
    /**
     * 绿信新对账文件sftpPath
     */
    @Value("${lvxin.new.loan.sftpPath}")
    private String loanSftpPath;
    //权益路径
    @Value("${lvxin.new.IncludingEquity.loan.sftpPath}")
    private String includingEquitySftpPath;

    public String getIncludingEquitySftpPath() {
        return includingEquitySftpPath;
    }

    public void setIncludingEquitySftpPath(String includingEquitySftpPath) {
        this.includingEquitySftpPath = includingEquitySftpPath;
    }

    public String getSftpUserName() {
        return sftpUserName;
    }

    public void setSftpUserName( String sftpUserName ) {
        this.sftpUserName = sftpUserName;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public void setSftpPassword( String sftpPassword ) {
        this.sftpPassword = sftpPassword;
    }

    public String getSftpIp() {
        return sftpIp;
    }

    public void setSftpIp( String sftpIp ) {
        this.sftpIp = sftpIp;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public void setSftpPort( Integer sftpPort ) {
        this.sftpPort = sftpPort;
    }

    public String getAgreementSftpPath(String applyNo) {
        return agreementSftpPath + DateUtil.formatShort(new Date()) + "/" + applyNo;
    }

    public void setAgreementSftpPath( String agreementSftpPath ) {
        this.agreementSftpPath = agreementSftpPath;
    }

    public String getIncludingEquityAgreementSftpPath(String applyNo) {
        return this.includingEquityAgreementSftpPath + DateUtil.formatShort(new Date()) + "/" + applyNo;
    }

    public void setIncludingEquityAgreementSftpPath(String includingEquityAgreementSftpPath) {
        this.includingEquityAgreementSftpPath = includingEquityAgreementSftpPath;
    }

    public String getClearVoucherSftpPath(String applyNo) {
        return clearVoucherSftpPath+ DateUtil.formatShort(new Date()) + "/" + applyNo;
    }

    public void setClearVoucherSftpPath( String clearVoucherSftpPath ) {
        this.clearVoucherSftpPath = clearVoucherSftpPath;
    }

    public String getLoanSftpPath() {
        return loanSftpPath;
    }

    public void setLoanSftpPath( String loanSftpPath ) {
        this.loanSftpPath = loanSftpPath;
    }

    public String getIncludingEquityClearVoucherSftpPath(String applyNo) {
        return this.IncludingEquityClearVoucherSftpPath + DateUtil.formatShort(new Date()) + "/" + applyNo;
    }

    public void setIncludingEquityClearVoucherSftpPath(String includingEquityClearVoucherSftpPath) {
        this.IncludingEquityClearVoucherSftpPath = includingEquityClearVoucherSftpPath;
    }
}
