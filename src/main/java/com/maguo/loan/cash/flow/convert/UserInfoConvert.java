package com.maguo.loan.cash.flow.convert;


import com.maguo.loan.cash.flow.entity.CreditUserContactInfo;
import com.maguo.loan.cash.flow.entity.CreditUserInfo;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserInfoConvert {
    UserInfoConvert INSTANCE = Mappers.getMapper(UserInfoConvert.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    void updateUserInfo(@MappingTarget UserInfo existUser, UserInfo newUser);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "revision", ignore = true)
    CreditUserInfo toCreditUserInfo(UserInfo userInfo);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "revision", ignore = true)
    CreditUserContactInfo toCreditUserContactInfo(UserContactInfo userContactInfo);

}
