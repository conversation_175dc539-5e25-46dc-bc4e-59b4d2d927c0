package com.maguo.loan.cash.flow.entrance.fql.dto;

import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/27 16:37
 **/
public class FqlCommonRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = -2189608329935642925L;

    /**
     * 合作方代码
     */
    @NotBlank(message = "partnerCode 不能为空")
    private String partnerCode;
    /**
     * 请求发送时间
     */
    @NotBlank(message = "timestamp 不能为空")
    private String timestamp;
    /**
     * 业务数据
     */
    @NotBlank(message = "bizContent 不能为空")
    private String bizContent;
    /**
     * 业务数据签名
     */
    @NotBlank(message = "sign 不能为空")
    private String sign;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getBizContent() {
        return bizContent;
    }

    public void setBizContent(String bizContent) {
        this.bizContent = bizContent;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
