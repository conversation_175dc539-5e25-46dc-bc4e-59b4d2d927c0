package com.maguo.loan.cash.flow.controller;

import com.jinghang.common.util.JsonUtil;
import com.jinghang.ppd.api.CollectionApi;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.ResultCode;
import com.jinghang.ppd.api.dto.collection.PpdReduceApplyRequest;
import com.jinghang.ppd.api.dto.collection.PpdReduceApplyResponse;
import com.maguo.loan.cash.flow.convert.CollectionConvert;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.PpdReduceApplyDTO;
import com.maguo.loan.cash.flow.entrance.ppd.service.PpdRepayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/collect")
public class CollectionController implements CollectionApi {

    private static final Logger logger = LoggerFactory.getLogger(CollectionController.class);

    @Autowired
    private PpdRepayService ppdRepayService;

    @Override
    public RestResult<PpdReduceApplyResponse> reduction(PpdReduceApplyRequest ppdReduceApplyRequest) {
        try{
            PpdReduceApplyDTO trialResultVo = CollectionConvert.INSTANCE.toVo(ppdReduceApplyRequest);
            logger.info("/collect/reduction 催收接口,入参：{}", JsonUtil.toJsonString(ppdReduceApplyRequest));
            PpdReduceApplyResponse reduction = ppdRepayService.reduction(trialResultVo);
            return RestResult.success(reduction);
        }catch (RuntimeException e){
            return RestResult.create(ResultCode.SYS_ERROR,e.getMessage(),null);
        }

    }
}
