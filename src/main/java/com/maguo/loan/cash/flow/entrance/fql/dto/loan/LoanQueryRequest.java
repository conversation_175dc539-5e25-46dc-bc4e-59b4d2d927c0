package com.maguo.loan.cash.flow.entrance.fql.dto.loan;

import jakarta.validation.constraints.NotBlank;

public class LoanQueryRequest {

    /**
     * 用信审批申请编号
     */
    @NotBlank(message = "用信审批申请编号不能为空")
    private String applyId;

    /**
     * 合作方代码
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }
}
