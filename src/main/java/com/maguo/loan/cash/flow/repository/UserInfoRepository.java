package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.UserInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface UserInfoRepository extends JpaRepository<UserInfo, String> {

    /**
     * 电话号码
     * @param phone 电话
     * @return 用户信息
     */
    UserInfo findByMobile(String phone);

    /**
     * 按照身份证查询
     * @param certNo 身份证
     * @return 用户信息
     */
    UserInfo findByCertNo(String certNo);


    /**
     * 通过id查找
     * @param id
     * @return
     */
    UserInfo findUserInfoById(String id);


    List<UserInfo> findAllByIdIn(List<String> userIdList);

    List<UserInfo> findAllByMobileIn(List<String> mobileList);

    Page<UserInfo> findByIdGreaterThan(String userId, PageRequest of);

    List<UserInfo> findAllByCertNoIn(List<String> idCardNos);

    /**
     * 根据身份证MD5查找用户信息
     * @param certNoMd5 身份证MD5
     * @return 用户信息
     */
    @Query(value = "SELECT * FROM user_info u WHERE MD5(u.cert_no) = :certNoMd5", nativeQuery = true)
    UserInfo findCertNoByMd5(@Param("certNoMd5") String certNoMd5);
}
