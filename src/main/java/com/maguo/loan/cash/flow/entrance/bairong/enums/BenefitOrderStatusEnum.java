package com.maguo.loan.cash.flow.entrance.bairong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权益订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum BenefitOrderStatusEnum {

    /**
     * 待支付
     */
    PENDING_PAYMENT(1, "待支付"),

    /**
     * 支付成功
     */
    PAY_SUCCESS(2, "支付成功"),

    /**
     * 支付失败
     */
    PAY_FAILED(3, "支付失败"),

    /**
     * 退款中
     */
    REFUNDING(4, "退款中"),

    /**
     * 退款成功
     */
    REFUND_SUCCESS(5, "退款成功"),

    /**
     * 退款失败
     */
    REFUND_FAILED(6, "退款失败"),

    /**
     * 订单取消
     */
    ORDER_CANCELED(7, "订单取消");

    /**
     * 状态编码（对应文档中status字段的数值）
     */
    private final Integer code;

    /**
     * 状态名称（对应文档中status字段的描述）
     */
    private final String name;

    /**
     * 根据编码获取枚举实例
     *
     * @param code 状态编码
     * @return 对应的枚举实例，无匹配时返回null
     */
    public static BenefitOrderStatusEnum getByCode(Integer code) {
        for (BenefitOrderStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 根据名称获取枚举实例
     *
     * @param name 状态名称
     * @return 对应的枚举实例，无匹配时返回null
     */
    public static BenefitOrderStatusEnum getByName(String name) {
        for (BenefitOrderStatusEnum statusEnum : values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum;
            }
        }
        return null;
    }
}
