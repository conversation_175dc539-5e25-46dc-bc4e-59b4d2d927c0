package com.maguo.loan.cash.flow.util;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * 计算金额
 */
public final class AmountUtil {
    private AmountUtil() {
    }

    public static <T> BigDecimal calcSumAmount(List<T> plans, Function<T, BigDecimal> func) {
        return plans.stream().map(func)
            .map(i -> Objects.requireNonNullElse(i, BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal sum(BigDecimal... amount) {
        return Arrays.stream(amount)
            .map(i -> Objects.requireNonNullElse(i, BigDecimal.ZERO))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal subtract(BigDecimal... amount) {
        return Arrays.stream(amount)
            .map(i -> Objects.requireNonNullElse(i, BigDecimal.ZERO))
            .reduce(BigDecimal::subtract)
            .orElse(BigDecimal.ZERO);
    }

    public static String bigDecimalToString(BigDecimal amount) {
        if (amount == null) {
            return null; // or return ""; based on your requirement
        }
        // Apply rounding and convert to String
        return amount.setScale(2, BigDecimal.ROUND_HALF_DOWN).toString();
    }

    public static BigDecimal safeAmount(BigDecimal amount) {
        return Objects.nonNull(amount) ? amount : BigDecimal.ZERO;
    }

    public static long safeAmountToLong(BigDecimal amount) {
        return Objects.nonNull(amount) ? amount.multiply(new BigDecimal("100")).longValue() : 0L;
    }

    public static BigDecimal multiplyAmount(BigDecimal amount, int number) {
        // 确保金额不为null，如果不为null，则乘以2；否则返回BigDecimal.ZERO
        return safeAmount(amount).multiply(BigDecimal.valueOf(number));
    }

    /**
     * 转为单位分
     *
     * @param amount
     * @return
     */
    public static BigDecimal toDivide(BigDecimal amount) {
        return Objects.isNull(amount) ? BigDecimal.ZERO : amount.multiply(new BigDecimal("100"));
    }
}
