package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.ThirdChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @since 2024-12-09
 */
@Entity
@Table(name = "third_user_info")
public class ThirdUserInfo extends BaseEntity {

    private String userId;

    private String thirdUserId;

    @Enumerated(jakarta.persistence.EnumType.STRING)
    private ThirdChannel thirdChannel;

    private String mobile;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getThirdUserId() {
        return thirdUserId;
    }

    public void setThirdUserId(String thirdUserId) {
        this.thirdUserId = thirdUserId;
    }

    public ThirdChannel getThirdChannel() {
        return thirdChannel;
    }

    public void setThirdChannel(ThirdChannel thirdChannel) {
        this.thirdChannel = thirdChannel;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Override
    protected String prefix() {
        return "QH";
    }
}
