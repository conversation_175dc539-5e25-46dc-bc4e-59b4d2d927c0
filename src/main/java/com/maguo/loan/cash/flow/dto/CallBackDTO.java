package com.maguo.loan.cash.flow.dto;


import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.FlowChannel;

/**
 * <AUTHOR>
 */

public class CallBackDTO {

    /**
     * 流量渠道
     */
    private FlowChannel flowChannel;

    /**
     * 业务id
     */
    private String businessId;

    /**
     * 回调类型
     */
    private CallbackState callbackState;

    /**
     * 备注
     */
    private String remark;

    /**
     * 回调次数
     */
    private int count = 1;

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public CallbackState getCallbackState() {
        return callbackState;
    }

    public void setCallbackState(CallbackState callbackState) {
        this.callbackState = callbackState;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
