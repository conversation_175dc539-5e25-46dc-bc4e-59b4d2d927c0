package com.maguo.loan.cash.flow.entrance.lvxin.filter.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * Created on 2018/11/23.
 */
public abstract class SHAUtils {

    private static Logger logger = LoggerFactory.getLogger(SHAUtils.class);

    public static final String ALGORITHM = "SHA-256";
    public static final int LOW_EIGHT = 255;

    /**
     * 对data字符进行sha-256处理，并进行base64处理
     *
     * @param data
     * @return
     */
    public static String sha256(String data) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance(ALGORITHM);
            messageDigest.update(data.getBytes(StandardCharsets.UTF_8));
            String result = byte2Hex(messageDigest.digest());
            return new String(Base64.getEncoder().encode(result.getBytes()), StandardCharsets.UTF_8);
        } catch (NoSuchAlgorithmException e) {
            logger.error("加签失败", e);
            throw new RuntimeException("加签失败");
        }
    }

    /**
     * byte 转16进制
     *
     * @param bytes
     * @return
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringbuilder = new StringBuilder();
        String temp;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & LOW_EIGHT); //取低8位
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringbuilder.append("0");
            }
            stringbuilder.append(temp);
        }
        return stringbuilder.toString();
    }

}
