package com.maguo.loan.cash.flow.entrance.bairong.convert;

import com.alibaba.fastjson2.JSON;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.convert.ConvertMappings;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.BaiRongApplyAccountInfo;
import com.maguo.loan.cash.flow.entity.BaiRongApplyBasicInfo;
import com.maguo.loan.cash.flow.entity.BaiRongApplyRecord;
import com.maguo.loan.cash.flow.entity.BaiRongLoanApply;
import com.maguo.loan.cash.flow.entity.BairongRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrder;
import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrderHistory;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderNotifyRequest;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.bairong.BairongBenefitOrder;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderNotifyRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.callBack.BairongBenefitOrderQueryResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongCreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongLoanRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.AccInfoList;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.RepayInfoList;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongLoanStatus;
import com.maguo.loan.cash.flow.entrance.common.covert.ProjectConfigMapperHelper;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ContactInfo;
import com.maguo.loan.cash.flow.enums.Gender;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.LoanPurpose;
import com.maguo.loan.cash.flow.enums.Marriage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.Relation;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.util.AgeUtil;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.GioPushUtil;
import com.maguo.loan.cash.flow.util.IdCardUtil;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.maguo.loan.cash.flow.entrance.common.covert.CommonApiCovert.toRelation;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融数据转换
 * @date 2025/9/11 9:29
 */
@Mapper(imports = {LocalDateTime.class, Marriage.class, Industry.class, Relation.class, DateTimeFormatter.class, AgeUtil.class,
    IdCardUtil.class, GioPushUtil.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = ConvertMappings.class)
public interface BairongConvert {

    BairongConvert INSTANCE = Mappers.getMapper(BairongConvert.class);
    int CERT_NO_CITY_LENGTH = 4;
    int CERT_NO_DISTRICT_LENGTH = 6;
    int THOUSAND = 1000;

    /**
     * 还款方式
     * SYS001	利随本清
     * SYS002	等额本息
     * SYS003	等额本金
     * SYS004	按期还息到期还本
     * SYS005	等额 本息(接收渠道还款计划)
     * SYS100	等本等息
     *
     * @param mtdCde 还款方式
     * @return 返回数据
     */
    public static Integer toMtdCdePub(String mtdCde) {
        return switch (mtdCde) {
            case "SYS002" -> 1;
            case "SYS003" -> 2;
            default -> 0;
        };
    }

    /**
     * 贷款用途字段映射
     * 70001	购买原材料
     * 70002	进货
     * 70003	购买设备
     * 70004	购买家具或家电
     * 70005	教育学习
     * 70006	个人或家庭消费
     * 70007	资金周转
     * 70010	其他
     * 70011	装修
     * 70012	旅游
     * 70013	婚庆
     * 70014	健康医疗
     *
     * @param loanPurpose 贷款用途
     * @return 返回数据
     */

    @Named("toLoanPurpose")
    public static LoanPurpose toLoanPurpose(String loanPurpose) {
        return switch (loanPurpose) {
            case "70001", "70002", "70003", "70004", "70006" -> LoanPurpose.SHOPPING;
            case "70005" -> LoanPurpose.EDUCATION;
            case "70007", "70010" -> LoanPurpose.OTHER;
            case "70011" -> LoanPurpose.DECORATION;
            case "70012" -> LoanPurpose.TOUR;
            case "70013" -> LoanPurpose.MARRIAGE;
            case "70014" -> LoanPurpose.HEALTH;
            default -> LoanPurpose.OTHER;
        };
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "request.accountInfo.acctKind", target = "acctKind")
    @Mapping(constant = "request.accountInfo.acctBankCode", target = "acctBankCode")
    @Mapping(source = "request.accountInfo.acctNo", target = "acctNo")
    @Mapping(source = "request.accountInfo.acctName", target = "acctName")
    @Mapping(source = "request.accountInfo.idTyp", target = "idTyp")
    @Mapping(source = "request.accountInfo.idNo", target = "idNo")
    @Mapping(source = "request.accountInfo.acctPhone", target = "acctPhone")
    @Mapping(source = "request.accountInfo.payChannel", target = "payChannel")
    @Mapping(source = "request.accountInfo.agreeNum", target = "agreeNum")
    BaiRongApplyAccountInfo toBairongApplyAccountInfo(BairongCreditApplyRequest request);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "request.loanInfo.outLoanSeq", target = "outLoanSeq")
    @Mapping(constant = "request.basicInfo.apptTyp", target = "apptTyp")
    @Mapping(source = "request.basicInfo.custName", target = "custName")
    @Mapping(source = "request.basicInfo.idTyp", target = "idTyp")
    @Mapping(source = "request.basicInfo.idTypOth", target = "idTypOth")
    @Mapping(source = "request.basicInfo.idNo", target = "idNo")
    @Mapping(source = "request.basicInfo.idNoStartDate", target = "idNoStartDate")
    @Mapping(source = "request.basicInfo.idNoEndDate", target = "idNoEndDate")
    @Mapping(source = "request.basicInfo.idOrgan", target = "idOrgan")
    @Mapping(expression = "java(IdCardUtil.getBirthDateAsString(request.getBasicInfo().getIdNo()))", target = "bornDate")
    @Mapping(source = "request.basicInfo.indivMobile", target = "indivMobile")
    @Mapping(expression = "java(GioPushUtil.getGenderFromIDCard(request.getBasicInfo().getIdNo()))", target = "indivSex")
    @Mapping(expression = "java(AgeUtil.calculateAge(request.getBasicInfo().getIdNo()))", target = "apptAge")
    @Mapping(source = "request.basicInfo.indivMarital", target = "indivMarital")
    @Mapping(source = "request.basicInfo.indivDegree", target = "indivDegree")
    @Mapping(source = "request.basicInfo.indivEdu", target = "indivEdu")
    @Mapping(source = "request.basicInfo.idCardAddress", target = "idCardAddress")
    @Mapping(source = "request.basicInfo.nation", target = "nation")
    @Mapping(source = "accountInfoId", target = "accountInfoId")
    BaiRongApplyBasicInfo toBaiRongApplyBasicInfo(BairongCreditApplyRequest request,String accountInfoId);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "request.loanInfo.outLoanSeq", target = "outLoanSeq")
    @Mapping(source = "basicInfoId", target = "applyBasicInfoId")
    @Mapping(source = "request.loanInfo.applCde", target = "applCde")
    @Mapping(expression = "java(LocalDate.now().format(DateTimeFormatter.ofPattern(\"yyyy-MM-dd\")))", target = "applyDt")
    @Mapping(source = "request.loanInfo.loanTyp", target = "loanTyp")
    @Mapping(source = "request.loanInfo.dnAmt", target = "dnAmt")
    @Mapping(source = "request.loanInfo.applyTnr", target = "applyTnr")
    @Mapping(source = "request.loanInfo.mtdCde", target = "mtdCde")
    @Mapping(source = "request.loanInfo.purpose", target = "purpose")
    @Mapping(source = "request.loanInfo.otherPurpose", target = "otherPurpose")
    @Mapping(source = "request.loanInfo.priceIntRat", target = "priceIntRat")
    @Mapping(source = "request.loanInfo.custRate", target = "custRate")
    @Mapping(source = "request.loanInfo.dueDayOpt", target = "dueDayOpt")
    @Mapping(source = "request.loanInfo.dueDay", target = "dueDay")
    @Mapping(constant = "request.loanInfo.directFlag", target = "directFlag")
    BaiRongLoanApply toBaiRongLoanApply(BairongCreditApplyRequest request, String basicInfoId);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "request.loanInfo.outLoanSeq", target = "orderNo")
    @Mapping(source = "request.basicInfo.custName", target = "name")
    @Mapping(source = "request.basicInfo.indivMobile", target = "mobile")
    @Mapping(source = "request.basicInfo.idNo", target = "certNo")
    @Mapping(source = "request.loanInfo.dnAmt", target = "applyAmount")
    @Mapping(source = "request.loanInfo.applyTnr", target = "applyPeriods")
    @Mapping(expression = "java(LocalDateTime.now())", target = "applyTime")
    @Mapping(constant = "INIT", target = "preOrderState")
    @Mapping(constant = "N", target = "isReject")
    @Mapping(constant = "SINGLE", target = "amountType")
    @Mapping(constant = "Y", target = "isAssignBankChannel")
    @Mapping(constant = "O", target = "equityRecipient")
    @Mapping(source = "request.projectCode", target = "projectCode")
    @Mapping(source = "request.loanInfo.loanTyp", target = "applyChannel", qualifiedByName = "toLoanPurpose")
    PreOrder toPreOrder(@MappingTarget PreOrder preOrder, BairongCreditApplyRequest request, ProjectConfigMapperHelper projectConfigMapperHelper);

    @AfterMapping
    default void completePreOrderMapping(@MappingTarget PreOrder preOrder, BairongLoanRequest request,
                                         ProjectConfigMapperHelper helper) {
        String projectCode = request.getProjectCode();
        if (projectCode != null && helper != null) {
            preOrder.setBankChannel(helper.getBankChannelFromProject(projectCode));
            preOrder.setFlowChannel(helper.getFlowChannelFromProject(projectCode));
        }
    }

    /**
     * 在基础映射完成后，处理影像列表，将其映射到对应的字段
     * @param record 映射的目标对象，已经被基础字段填充
     * @param request 原始请求对象，用于获取影像列表
     */
    @AfterMapping
    default void mapImagesFromInfoList(@MappingTarget BaiRongApplyRecord record, BairongCreditApplyRequest request) {
        if (request == null || request.getImageInfoList() == null || request.getImageInfoList().isEmpty()) {
            return;
        }
        for (BairongCreditApplyRequest.ImageInfo imageInfo : request.getImageInfoList()) {
            if (imageInfo == null || imageInfo.getImageType() == null || imageInfo.getImageUrl() == null) {
                continue;
            }

            switch (imageInfo.getImageType()) {
                case "1": // 身份证头像面
                    record.setIdPositive(imageInfo.getImageUrl());
                    break;
                case "2": // 身份证国徽面
                    record.setIdNegative(imageInfo.getImageUrl());
                    break;
                case "3": // 人脸识别照
                    record.setLivePhoto(imageInfo.getImageUrl());
                    break;
                default:
                    break;
            }
        }
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(source = "request.loanInfo.outLoanSeq", target = "orderNo")
    @Mapping(source = "request.basicInfo.custName", target = "name")
    @Mapping(source = "request.basicInfo.indivMobile", target = "mobile")
    @Mapping(source = "request.basicInfo.indivEdu", target = "education")
    @Mapping(source = "request.basicInfo.idNo", target = "idCardNo")
    @Mapping(source = "request.basicInfo.idNoStartDate", target = "idStartTime")
    @Mapping(source = "request.basicInfo.idNoEndDate", target = "idEndTime")
    @Mapping(source = "request.basicInfo.idCardAddress", target = "idAddress")
    @Mapping(source = "request.basicInfo.indivSex", target = "idSex")
    @Mapping(source = "request.basicInfo.nation", target = "idEthnic")
    @Mapping(source = "request.basicInfo.idOrgan", target = "idIssueOrg")
    @Mapping(source = "request.basicInfo.indivMarital", target = "marriage")
    @Mapping(source = "request.occupationInfo.profession", target = "job")
    @Mapping(source = "request.occupationInfo.indivEmpName", target = "workUnitName")
    @Mapping(source = "request.occupationInfo.monthlyIncome", target = "monthlyIncome")
    @Mapping(source = "request.occupationInfo.indivEmpAddInfo.province", target = "workUnitProvinceCode")
    @Mapping(source = "request.occupationInfo.indivEmpAddInfo.city", target = "workUnitCityCode")
    @Mapping(source = "request.occupationInfo.indivEmpAddInfo.country", target = "workUnitAreaCode")
    @Mapping(source = "request.loanInfo.purpose", target = "loanPurpose")
    @Mapping(source = "request.familyInfo.liveAddInfo.province", target = "livingProvince")
    @Mapping(source = "request.familyInfo.liveAddInfo.city", target = "livingCity")
    @Mapping(source = "request.familyInfo.liveAddInfo.country", target = "livingArea")
    @Mapping(source = "request.familyInfo.liveAddInfo.detailAddress", target = "livingAddress")
    @Mapping(source = "request.loanInfo.loanTyp", target = "isIncludingEquity")

    @Mapping(source = "request.extendInfo.custTransScore1", target = "custTransScore1")
    @Mapping(source = "request.extendInfo.custTransScore3", target = "custTransScore3")
    @Mapping(source = "request.extendInfo.custTransScore5", target = "custTransScore5")
    @Mapping(source = "request.extendInfo.teSource", target = "teSource")
    @Mapping(constant = "O", target = "equityRecipient")
    BaiRongApplyRecord toBaiRongApplyRecord(@MappingTarget BaiRongApplyRecord applyRecord, BairongCreditApplyRequest request);

    /**
     * 还款试算调用资方后部分响应字段转换
     *
     * @param trialResultVo 调用资方后返回参数
     * @return 转换后的响应参数
     */
    @Mappings({
        @Mapping(source = "amount", target = "totalAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "principal", target = "psRemPrcp", qualifiedByName = "safeAmount"),
        @Mapping(source = "interest", target = "odPrcpAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "penalty", target = "odIntAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "breachFee", target = "penlAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "consultFee", target = "odFeeAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "guaranteeFee", target = "odGrtAmt", qualifiedByName = "safeAmount")
    })
    BairongRepayTrialResponse toRepayTrailRes(TrialResultVo trialResultVo);

    /**
     * 将回调请求参数转换为百融权益订单实体
     *
     * @param request 回调请求参数
     * @param order   百融权益订单实体
     * @return 更新后的百融权益订单实体
     */
    BairongBenefitOrder toBairongBenefitOrder(@MappingTarget BairongBenefitOrder order, BairongBenefitOrderNotifyRequest request);

    /**
     * 将回调请求参数转换为百融权益订单历史表实体
     *
     * @param request 回调请求参数
     * @param order   百融权益订单历史表实体
     * @return 更新后的百融权益订历史表单实体
     */
    BairongBenefitOrderHistory toBairongBenefitOrderHistory(@MappingTarget BairongBenefitOrderHistory order, BairongBenefitOrderNotifyRequest request);

    /**
     * 将响应参数转换为百融权益订单历史表实体
     *
     * @param queryResponse 查询响应参数
     * @param order   百融权益订单历史表实体
     * @return 更新后的百融权益订单历史表实体
     */
    BairongBenefitOrderHistory toBairongBenefitOrderHistory(@MappingTarget BairongBenefitOrderHistory order,  BairongBenefitOrderQueryResponse queryResponse);
    /**
     * 还款申请初始化申请记录时部分字段转换
     *
     * @param accInfoList   账号信息
     * @param repayInfoList 账单信息
     * @return 申请记录
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(source = "repayInfoList.loanNo", target = "outLoanId")
    @Mapping(source = "repayInfoList.outBatchRepaymentSeq", target = "outRepayId")
    @Mapping(source = "accInfoList.acctNo", target = "repayCardId")
    @Mapping(source = "repayInfoList.period", target = "period")
    BairongRepayApplyRecord toRepayApplyRecord(AccInfoList accInfoList, RepayInfoList repayInfoList);

    /**
     * 线上还款参数转换
     *
     * @param loanId       放款id
     * @param period       期数
     * @param repayPurpose 还款类型
     * @return 转换后的参数
     */
    @Mapping(source = "loanId", target = "loanId")
    @Mapping(source = "period", target = "period")
    @Mapping(source = "repayPurpose", target = "repayPurpose")
    @Mapping(constant = "ONLINE", target = "repayMode")
    @Mapping(constant = "REPAY", target = "repayType")
    @Mapping(constant = "CAPITAL", target = "paySide")
    @Mapping(constant = "USER", target = "operationSource")
    OnlineRepayApplyRequest toOnlineApplyRequest(String loanId, Integer period, RepayPurpose repayPurpose);

    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "preOrder.openId", target = "id")
    @Mapping(source = "preOrder.certNo", target = "certNo")
    @Mapping(source = "preOrder.mobile", target = "mobile")
    @Mapping(source = "preOrder.name", target = "name")
    @Mapping(source = "applyRecord.marriage", target = "marriage")
    @Mapping(source = "applyRecord.education", target = "education")
    @Mapping(source = "applyRecord.monthlyIncome", target = "income")
    @Mapping(source = "applyRecord.industry", target = "industry")
    @Mapping(source = "applyRecord.job", target = "position")
    @Mapping(source = "applyRecord.email", target = "email")
    @Mapping(source = "applyRecord.livingAddress", target = "livingAddress")
    @Mapping(source = "applyRecord.livingProvinceCode", target = "livingProvinceCode", qualifiedByName = "toProvinceCode")
    @Mapping(source = "applyRecord.livingCityCode", target = "livingCityCode", qualifiedByName = "toCityCode")
    @Mapping(source = "applyRecord.livingAreaCode", target = "livingDistrictCode", qualifiedByName = "toDistrictCode")
    @Mapping(source = "applyRecord.livingAddress", target = "livingStreet")
    @Mapping(source = "applyRecord.workUnitName", target = "unit")
    @Mapping(source = "applyRecord.workUnitAddress", target = "unitAddress")
    @Mapping(source = "applyRecord.workUnitProvinceCode", target = "unitProvinceCode", qualifiedByName = "toProvinceCode")
    @Mapping(source = "applyRecord.workUnitCityCode", target = "unitCityCode", qualifiedByName = "toCityCode")
    @Mapping(source = "applyRecord.workUnitAreaCode", target = "unitDistrictCode", qualifiedByName = "toDistrictCode")
    @Mapping(source = "applyRecord.workUnitAddress", target = "unitStreet")
    UserInfo toUserInfo(PreOrder preOrder, BaiRongApplyRecord applyRecord);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "idCardNo", target = "certNo")
    @Mapping(source = "idAddress", target = "certAddress")
    @Mapping(source = "idIssueOrg", target = "certSignOrg")
    @Mapping(source = "idStartTime", target = "certValidStart")
    @Mapping(source = "idEndTime", target = "certValidEnd")
    @Mapping(source = "idPositive", target = "headOssKey")
    @Mapping(source = "idNegative", target = "nationOssKey")
    @Mapping(source = "idCardNo", target = "provinceCode", qualifiedByName = "toProvinceCode")
    @Mapping(source = "idCardNo", target = "cityCode", qualifiedByName = "toCityCode")
    @Mapping(source = "idCardNo", target = "districtCode", qualifiedByName = "toDistrictCode")
    @Mapping(source = "idSex", target = "gender", qualifiedByName = "toGender")
    @Mapping(source = "idEthnic", target = "nation")
    UserOcr toUserOcr(BaiRongApplyRecord applyRecord);

    @Named("toProvinceCode")
    default String toProvinceCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, 2) + "0000";
    }

    @Named("toCityCode")
    default String toCityCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, CERT_NO_CITY_LENGTH) + "00";
    }

    @Named("toDistrictCode")
    default String toDistrictCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, CERT_NO_DISTRICT_LENGTH);
    }

    @Named("toGender")
    static Gender toGender(String idSex) {
        if (StringUtils.isBlank(idSex)) {
            return Gender.UNKNOWN;
        }
        return switch (idSex) {
            case "1" -> Gender.MALE;
            case "2" -> Gender.FEMALE;
            default -> Gender.UNKNOWN;
        };
    }

    @Named("toRelationJsonArrayStr")
    default String toRelationJsonArrayStr(List<ContactInfo> contactInfos) {
        List<UserContactInfo> userContactInfos = new ArrayList<>();
        for (ContactInfo contactInfo : contactInfos) {
            UserContactInfo obj = new UserContactInfo();
            obj.setName(contactInfo.getName());
            obj.setPhone(contactInfo.getPhone());
            String relation = contactInfo.getRelation();
            obj.setRelation(toRelation(relation));
            userContactInfos.add(obj);
        }
        return JSON.toJSONString(userContactInfos);
    }

    static BairongLoanStatus toBairongLoanStatusByNewFlow(OrderState orderState) {
        return switch (orderState) {
            case INIT, AUDIT_PASS, LOANING, CREDITING, CREDIT_PASS, SUSPENDED -> BairongLoanStatus.LOANING;
            case LOAN_FAIL, LOAN_CANCEL, CREDIT_FAIL -> BairongLoanStatus.LOAN_FAIL;
            case LOAN_PASS, CLEAR -> BairongLoanStatus.LOAN_PASS;
            default -> null;
        };
    }


     static String toBairongRepayMethod(RepayMode repayMethod) {
        if (repayMethod == repayMethod.ONLINE) {
            return "1";
        } else if (repayMethod == repayMethod.OFFLINE) {
            return "3";
        } else  {
            return "2";
        }
    }

    @Named("safeAmount")
    default BigDecimal safeAmount(BigDecimal amount) {
        return AmountUtil.safeAmount(amount);
    }

}
