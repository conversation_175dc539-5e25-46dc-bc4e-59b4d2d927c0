package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

/**
 * @ClassName RepayRequest
 * <AUTHOR>
 * @Description 还款申请响应参数
 * @Date 2025/8/6 14:37
 * @Version v1.0
 **/
public class FenQiLeRepayResponse {

    /**
     * 还款状态
     * 1、接口通知成功
     * 2、接口通知失败
     * (具体的结果都以查询接口为准)
     */
    private Integer status;

    /**
     * 还款描述:失败时，需给出的错误描述
     */
    private String msg;

    /**
     * 还款ID
     */
    private String partnerRepaymentGid;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getPartnerRepaymentGid() {
        return partnerRepaymentGid;
    }

    public void setPartnerRepaymentGid(String partnerRepaymentGid) {
        this.partnerRepaymentGid = partnerRepaymentGid;
    }

    public static FenQiLeRepayResponse fail(String msg) {
        FenQiLeRepayResponse result = new FenQiLeRepayResponse();
        result.setStatus(2);
        result.setMsg(msg);
        return result;
    }
}
