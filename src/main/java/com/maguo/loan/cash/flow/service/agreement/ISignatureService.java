package com.maguo.loan.cash.flow.service.agreement;


import com.maguo.loan.cash.flow.remote.nfsp.req.AgreementSignReq;
import com.maguo.loan.cash.flow.remote.nfsp.rsp.AgreementSignResult;
import com.maguo.loan.cash.flow.remote.nfsp.rsp.AgreementSignRsp;

/**
 * <AUTHOR>
 * @date 2024/6/29
 */
public interface ISignatureService {
    /**
     * 签署申请
     * @param signReq
     * @return
     */
    AgreementSignRsp signApply(AgreementSignReq signReq);

    /**
     * 签署结果查询
     * @param taskId
     * @param userId
     * @return
     */
    AgreementSignResult signQuery(String taskId, String userId);

}
