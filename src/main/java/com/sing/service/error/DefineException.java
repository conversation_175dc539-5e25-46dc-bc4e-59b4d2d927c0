package com.sing.service.error;

/**
 * description 自定义全局异常
 */
public class DefineException extends Exception {

	private Exception e;

	public DefineException( String msg ) {
		this.e = new Exception(msg);
	}

	public DefineException( String msg, Throwable cause ) {
		this.e = new Exception(msg,cause);
	}

	public DefineException(){

	}

	public Exception getE() {
		return e;
	}

	public void setE(Exception e) {
		this.e = e;
	}




}
