/**
 * 移动端检测工具
 */

// 检测是否为移动设备
export function isMobile() {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android', 'webos', 'iphone', 'ipad', 'ipod', 
    'blackberry', 'iemobile', 'opera mini', 'mobile'
  ]
  
  return mobileKeywords.some(keyword => userAgent.includes(keyword))
}

// 检测是否为微信环境
export function isWechat() {
  const userAgent = navigator.userAgent.toLowerCase()
  return userAgent.includes('micromessenger')
}

// 检测是否为企业微信环境
export function isWechatWork() {
  const userAgent = navigator.userAgent.toLowerCase()
  return userAgent.includes('wxwork')
}

// 检测设备类型
export function getDeviceType() {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (userAgent.includes('iphone') || userAgent.includes('ipod')) {
    return 'iphone'
  } else if (userAgent.includes('ipad')) {
    return 'ipad'
  } else if (userAgent.includes('android')) {
    return 'android'
  } else if (isMobile()) {
    return 'mobile'
  } else {
    return 'desktop'
  }
}

// 获取屏幕尺寸信息
export function getScreenInfo() {
  return {
    width: window.screen.width,
    height: window.screen.height,
    availWidth: window.screen.availWidth,
    availHeight: window.screen.availHeight,
    devicePixelRatio: window.devicePixelRatio || 1
  }
}

// 检测是否支持触摸
export function isTouchDevice() {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

// 移动端页面跳转
export function redirectToMobile(path = '/mobile/dashboard') {
  if (isMobile() && !window.location.pathname.startsWith('/mobile')) {
    window.location.href = path
  }
}

// PC端页面跳转
export function redirectToDesktop(path = '/dashboard') {
  if (!isMobile() && window.location.pathname.startsWith('/mobile')) {
    window.location.href = path
  }
}

// 设置移动端viewport
export function setMobileViewport() {
  if (isMobile()) {
    let viewport = document.querySelector('meta[name="viewport"]')
    if (!viewport) {
      viewport = document.createElement('meta')
      viewport.name = 'viewport'
      document.head.appendChild(viewport)
    }
    viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
  }
}

// 禁用移动端双击缩放
export function disableDoubleTapZoom() {
  if (isMobile()) {
    let lastTouchEnd = 0
    document.addEventListener('touchend', function (event) {
      const now = (new Date()).getTime()
      if (now - lastTouchEnd <= 300) {
        event.preventDefault()
      }
      lastTouchEnd = now
    }, false)
  }
}

// 移动端适配初始化
export function initMobileAdaptation() {
  setMobileViewport()
  disableDoubleTapZoom()
  
  // 添加设备类型class
  const deviceType = getDeviceType()
  document.body.classList.add(`device-${deviceType}`)
  
  if (isMobile()) {
    document.body.classList.add('is-mobile')
  }
  
  if (isWechat()) {
    document.body.classList.add('is-wechat')
  }
  
  if (isWechatWork()) {
    document.body.classList.add('is-wechat-work')
  }
}

// 获取企业微信环境信息
export function getWechatWorkInfo() {
  if (!isWechatWork()) {
    return null
  }
  
  return {
    isWechatWork: true,
    userAgent: navigator.userAgent,
    // 这里可以添加更多企业微信特定的信息获取
  }
}

// 移动端页面标题设置
export function setMobileTitle(title) {
  document.title = title
  
  // 企业微信环境下的标题设置
  if (isWechatWork() && window.wx && window.wx.config) {
    // 这里可以调用企业微信JSSDK设置标题
  }
}

export default {
  isMobile,
  isWechat,
  isWechatWork,
  getDeviceType,
  getScreenInfo,
  isTouchDevice,
  redirectToMobile,
  redirectToDesktop,
  setMobileViewport,
  disableDoubleTapZoom,
  initMobileAdaptation,
  getWechatWorkInfo,
  setMobileTitle
}
