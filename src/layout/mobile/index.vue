<template>
  <div class="mobile-layout">
    <!-- 移动端头部 -->
    <div class="mobile-header" v-if="showHeader">
      <div class="header-left">
        <i class="el-icon-arrow-left" @click="goBack" v-if="showBackButton"></i>
        <span class="header-title">{{ pageTitle }}</span>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand" trigger="click">
          <span class="user-info">
            <img :src="userAvatar" class="user-avatar" />
            <i class="el-icon-arrow-down"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="profile">个人中心</el-dropdown-item>
            <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <!-- 移动端主体内容 -->
    <div class="mobile-main" :class="{ 'has-header': showHeader }">
      <router-view />
    </div>

    <!-- 移动端底部导航 -->
    <div class="mobile-tabbar" v-if="showTabbar">
      <div
        class="tabbar-item"
        :class="{ active: activeTab === item.name }"
        v-for="item in tabbarItems"
        :key="item.name"
        @click="switchTab(item)"
      >
        <i :class="item.icon"></i>
        <span>{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Avatar from '@/assets/images/avatar.png'

export default {
  name: 'MobileLayout',
  data() {
    return {
      userAvatar: Avatar,
      tabbarItems: [
        {
          name: 'report',
          label: '报表',
          icon: 'el-icon-s-data',
          path: '/mobile/report'
        },
        {
          name: 'profile',
          label: '我的',
          icon: 'el-icon-user',
          path: '/mobile/profile'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['user']),
    pageTitle() {
      return this.$route.meta.title || '移动端'
    },
    showHeader() {
      return this.$route.meta.showHeader !== false
    },
    showTabbar() {
      return this.$route.meta.showTabbar !== false
    },
    showBackButton() {
      return this.$route.meta.showBack === true
    },
    activeTab() {
      const path = this.$route.path
      const item = this.tabbarItems.find(item => path.startsWith(item.path))
      return item ? item.name : ''
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    switchTab(item) {
      if (this.$route.path !== item.path) {
        this.$router.push(item.path)
      }
    },
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/mobile/profile')
          break
        case 'logout':
          this.logout()
          break
      }
    },
    logout() {
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$router.push('/mobile/login')
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.mobile-header {
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;

  .header-left {
    display: flex;
    align-items: center;

    .el-icon-arrow-left {
      font-size: 20px;
      margin-right: 10px;
      color: #333;
      cursor: pointer;
    }

    .header-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }
  }

  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;

      .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 5px;
      }

      .el-icon-arrow-down {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

.mobile-main {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  &.has-header {
    padding-top: 50px;
  }
}

.mobile-tabbar {
  height: 60px;
  background: #fff;
  display: flex;
  border-top: 1px solid #e5e5e5;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;

  .tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: color 0.3s;

    i {
      font-size: 22px;
      margin-bottom: 4px;
      color: #999;
    }

    span {
      font-size: 12px;
      color: #999;
    }

    &.active {
      i, span {
        color: #1A7EFD;
      }
    }

    &:active {
      background-color: #f5f5f5;
    }
  }
}

// 为有底部导航的页面添加底部间距
.mobile-main:not(.no-tabbar) {
  padding-bottom: 60px;
}
</style>
