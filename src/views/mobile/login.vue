<template>
  <div class="mobile-login">
    <div class="login-header">
      <img :src="Logo" alt="Logo" class="logo" />
      <h2 class="title">捷易花</h2>
    </div>

    <div class="login-content">
      <!-- 企业微信登录按钮 -->
      <div class="wechat-login" v-if="!isWechatEnv">
        <el-button
          type="primary"
          size="large"
          class="wechat-btn"
          @click="redirectToWechat"
          :loading="loading"
        >
          <i class="el-icon-message"></i>
          企业微信授权登录
        </el-button>

        <!-- 账密登录切换按钮 -->
        <div class="switch-login">
          <span class="switch-text" @click="togglePasswordLogin">账密登录</span>
        </div>
      </div>

      <!-- 企业微信环境自动登录 -->
      <div class="auto-login" v-else>
        <div class="loading-wrapper">
          <i class="el-icon-loading"></i>
          <p>正在获取用户信息...</p>
        </div>
      </div>

      <!-- 账号密码登录 -->
      <div class="password-login" v-if="showPasswordLogin && !isWechatEnv">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              prefix-icon="el-icon-user"
              size="large"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="el-icon-lock"
              size="large"
              @keyup.enter.native="handleLogin"
            />
          </el-form-item>
          <el-form-item prop="code">
            <div class="code-input-wrapper">
              <el-input
                v-model="loginForm.code"
                placeholder="请输入验证码"
                prefix-icon="el-icon-key"
                size="large"
                class="code-input"
                @keyup.enter.native="handleLogin"
              />
              <div class="code-image" @click="getCode">
                <img :src="codeUrl" alt="验证码" v-if="codeUrl" />
                <div class="code-loading" v-else>
                  <i class="el-icon-loading"></i>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-btn"
            @click="handleLogin"
            :loading="loginLoading"
          >
            登录
          </el-button>
        </el-form>

        <!-- 返回企业微信登录 -->
        <div class="back-to-wechat">
          <span class="back-text" @click="togglePasswordLogin">返回企业微信登录</span>
        </div>
      </div>
    </div>

    <div class="login-footer">
      <p class="copyright">© 2024 京航金融 版权所有</p>
    </div>
  </div>
</template>

<script>
import Logo from '@/assets/images/logo.png'
import { encrypt } from '@/utils/rsaEncrypt'
import { getCodeImg } from '@/api/login'

export default {
  name: 'MobileLogin',
  data() {
    return {
      Logo,
      loading: false,
      loginLoading: false,
      isWechatEnv: false,
      showPasswordLogin: false, // 控制账密登录显示
      codeUrl: '',
      loginForm: {
        username: '',
        password: '',
        code: '',
        uuid: ''
      },
      loginRules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.checkWechatEnv()
    this.handleWechatCallback()
  },
  methods: {
    // 切换账密登录显示
    togglePasswordLogin() {
      this.showPasswordLogin = !this.showPasswordLogin
      // 显示账密登录时获取验证码
      if (this.showPasswordLogin) {
        this.getCode()
      }
    },

    // 获取验证码
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = res.img
        this.loginForm.uuid = res.uuid
      }).catch(() => {
        this.$message.error('获取验证码失败')
      })
    },

    // 检查是否在企业微信环境
    checkWechatEnv() {
      const ua = navigator.userAgent.toLowerCase()
      this.isWechatEnv = ua.includes('wxwork') || ua.includes('micromessenger')

      if (this.isWechatEnv) {
        this.initWechatLogin()
      }
    },

    // 初始化企业微信登录
    initWechatLogin() {
      // 这里需要引入企业微信JSSDK
      // 实际项目中需要配置企业微信应用信息
      this.getWechatUserInfo()
    },

    // 获取企业微信用户信息
    async getWechatUserInfo() {
      try {
        // 模拟企业微信用户信息获取
        // 实际实现需要调用企业微信JSSDK
        setTimeout(() => {
          const mockUserInfo = {
            userid: 'test_user',
            name: '测试用户',
            avatar: ''
          }
          this.wechatLogin(mockUserInfo)
        }, 2000)
      } catch (error) {
        this.$message.error('获取用户信息失败')
        console.error('获取企业微信用户信息失败:', error)
      }
    },

    // 企业微信登录
    async wechatLogin(userInfo) {
      try {
        // 调用后端企业微信登录接口
        const response = await this.$store.dispatch('WechatLogin', userInfo)
        if (response) {
          this.$message.success('登录成功')
          this.$router.push('/mobile/report')
        }
      } catch (error) {
        this.$message.error('登录失败')
        console.error('企业微信登录失败:', error)
      }
    },

    // 跳转到企业微信授权页面
    redirectToWechat() {
      this.loading = true
      // 构建企业微信授权URL
      const corpId = process.env.VUE_APP_WECHAT_CORP_ID || 'your_corp_id'
      const redirectUri = encodeURIComponent(window.location.href)
      const state = 'mobile_login'

      const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corpId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&state=${state}#wechat_redirect`

      window.location.href = authUrl
    },

    // 处理企业微信回调
    handleWechatCallback() {
      const urlParams = new URLSearchParams(window.location.search)
      const code = urlParams.get('code')
      const state = urlParams.get('state')

      if (code && state === 'mobile_login') {
        this.handleWechatCode(code)
      }
    },

    // 处理企业微信授权码
    async handleWechatCode(code) {
      try {
        this.loading = true
        // 调用后端接口用code换取用户信息并登录
        const response = await this.$store.dispatch('WechatCodeLogin', { code })
        if (response) {
          this.$message.success('登录成功')
          this.$router.push('/mobile/report')
        }
      } catch (error) {
        this.$message.error('授权登录失败')
        console.error('企业微信授权登录失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 开发环境账号密码登录
    handleLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          this.loginLoading = true
          try {
            const loginData = {
              username: this.loginForm.username,
              password: encrypt(this.loginForm.password),
              code: this.loginForm.code,
              uuid: this.loginForm.uuid,
              rememberMe: false
            }

            await this.$store.dispatch('Login', loginData)
            this.$message.success('登录成功')
            this.$router.push('/mobile/report')
          } catch (error) {
            this.$message.error('登录失败')
            // 登录失败后重新获取验证码
            this.getCode()
            // 清空验证码输入
            this.loginForm.code = ''
          } finally {
            this.loginLoading = false
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-top: 60px;
  margin-bottom: 60px;

  .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
  }

  .title {
    color: #fff;
    font-size: 24px;
    font-weight: 300;
    margin: 0;
  }
}

.login-content {
  flex: 1;

  .wechat-login {
    text-align: center;
    margin-bottom: 40px;

    .wechat-btn {
      width: 100%;
      height: 50px;
      font-size: 16px;
      border-radius: 25px;
      background: #07c160;
      border-color: #07c160;

      &:hover {
        background: #06ad56;
        border-color: #06ad56;
      }

      i {
        margin-right: 8px;
      }
    }

    .switch-login {
      margin-top: 30px;

      .switch-text {
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        cursor: pointer;
        text-decoration: underline;
        transition: color 0.3s;

        &:hover {
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }

  .auto-login {
    text-align: center;
    padding: 40px 0;

    .loading-wrapper {
      color: #fff;

      i {
        font-size: 32px;
        margin-bottom: 15px;
        display: block;
      }

      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }

  .password-login {
    .login-form {
      .el-form-item {
        margin-bottom: 20px;
      }

      // 验证码输入框样式
      .code-input-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;

        .code-input {
          flex: 1;
        }

        .code-image {
          width: 100px;
          height: 50px;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          background: rgba(255, 255, 255, 0.1);
          border: 2px solid rgba(255, 255, 255, 0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s;

          &:hover {
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .code-loading {
            color: rgba(255, 255, 255, 0.7);
            font-size: 18px;

            i {
              animation: rotating 2s linear infinite;
            }
          }
        }
      }

      .login-btn {
        width: 100%;
        height: 50px;
        font-size: 16px;
        border-radius: 25px;
      }
    }

    .back-to-wechat {
      text-align: center;
      margin-top: 20px;

      .back-text {
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        cursor: pointer;
        text-decoration: underline;
        transition: color 0.3s;

        &:hover {
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 40px;

  .copyright {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin: 0;
  }
}

::v-deep .el-divider__text {
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
}

::v-deep .el-divider {
  border-color: rgba(255, 255, 255, 0.3);
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
