<template>
  <div class="app-container">
    <div style="margin-bottom: 10px;">
      <el-button round type="primary" size="mini" @click="open">新增</el-button>
    </div>

    <el-table v-loading="loading" border :data="list" :cell-style="tableRowStyle">
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{ parseInt(scope.$index) + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="流量方" prop="flowChannel" align="center">
        <template slot-scope="scope">
          <dict-tag :value="scope.row.flowChannel" :options="dict.flowChannel" />
        </template>
      </el-table-column>
      <el-table-column label="资方" prop="bankChannel" align="center">
        <template slot-scope="scope">
          <dict-tag :value="scope.row.bankChannel" :options="dict.bankChannel" />
        </template>
      </el-table-column>
      <el-table-column
        label="日授信限额/万元"
        prop="creditDayLimit"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.creditDayLimit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="日放款限额/万元"
        prop="loanDayLimit"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.loanDayLimit }}</span>
        </template>
      </el-table-column>
      <el-table-column label="借款期限" prop="periodsRange" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.periodsRange }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" prop="updatedTime" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.updatedTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改人" prop="updatedBy" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.updatedBy }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="150" align="center" style="opacity: 1;">
        <template slot-scope="scope">
          <el-button
            style="padding: 0;"
            type="text"
            size="mini"
            @click="detail(scope.row.id)"
            >查看</el-button
          >
          <el-button
            style="padding: 0;"
            type="text"
            size="mini"
            @click="edite(scope.row.id)"
            >修改</el-button
          >
          <el-button
            style="padding: 0;"
            v-if="scope.row.enabled === 'DISABLE'"
            type="text"
            size="mini"
            @click="toggle(scope.row.id, 'ENABLE')"
            >启用</el-button
          >
          <el-button
            style="padding: 0; color: #c00;"
            v-if="scope.row.enabled === 'ENABLE'"
            type="text"
            size="mini"
            @click="toggle(scope.row.id, 'DISABLE')"
            >停用</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增 -->
    <el-dialog :visible.sync="visible" :title="title" width="720px" :before-close="close">
      <el-form
        label-position="left"
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        :disabled="mode === 'detail'"
      >
        <el-form-item label="流量方" prop="flowChannel">
          <el-select
            placeholder="请选择"
            style="width:300px"
            v-model="ruleForm.flowChannel"
          >
            <el-option
              v-for="item in flowChannelOptions"
              :key="item.name"
              :label="item.desc"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资方" prop="bankChannel">
          <el-select
            placeholder="请选择"
            style="width:300px"
            v-model="ruleForm.bankChannel"
          >
            <el-option
              v-for="item in bankChannelOptions"
              :key="item.desc"
              :label="item.desc"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="借款期限" prop="periodsRange">
          <el-checkbox-group v-model="ruleForm.periodsRange">
            <el-checkbox v-for="item in periodsRangeOptions" :key="item.value" :label="item.value"
            >{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="日授信额上限" prop="creditDayLimit">
          <el-input type="number"
            style="width:300px; margin-right: 10px;"
            v-model="ruleForm.creditDayLimit"
          ></el-input
          >万元
        </el-form-item>
        <el-form-item label="日放款额上限" prop="loanDayLimit">
          <el-input type="number"
            style="width:300px; margin-right: 10px;"
            v-model="ruleForm.loanDayLimit"
          ></el-input
          >万元
        </el-form-item>

        <el-form-item
          style="display: flex; justify-content: flex-end; padding-top: 20px;"
          v-if="mode !== 'detail'"
        >
          <el-button round @click="close">取消</el-button>
          <el-button round type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  PPDqueryCapitalConfigPage,
  PPDqueryCapital,
  PPDgetCapitalConfig,
  PPDsaveCapitalConfig,
  PPDgetFlowConfig,
  PPDupdateEnabled
} from "@/api/setting";

export default {
  name: "",
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      title: "新增",
      total: 0,
      loading: false,
      visible: false,
      list: [],
      mode: "",
      ruleForm: {
        flowChannel: "", // 资金方
        bankChannel: "", // 资金方
        periodsRange: [], // 支持期数
        creditDayLimit: "", // 资方授信日限额
        loanDayLimit: "", // 资方放款日限额
      },
      rules: {
        flowChannel: [
          { required: true, message: "流量方不能为空", trigger: "change" }
        ],
        bankChannel: [
          { required: true, message: "资方不能为空", trigger: "change" }
        ],
        creditDayLimit: [
          { required: true, message: "日授信额上限不能为空", trigger: "blur" },
          { validator: this.validateAmount, trigger: "blur" }
        ],
        loanDayLimit: [
          { required: true, message: "日放款额下限不能为空", trigger: "blur" },
          { validator: this.validateAmount, trigger: "blur" }
        ]
      },
      bankChannelOptions: [],
      flowChannelOptions: [],
      periodsRangeOptions: [
        { label: "3期",  value: "3" },
        { label: "6期",  value: "6" },
        { label: "9期",  value: "9" },
        { label: "12期",  value: "12" }
      ],
    };
  },
  dicts: ["protocolChannel", "flowChannel", "bankChannel"],
  created() {
    this.getList();
    // 当前可选资方和流量方
    this.getDict();
  },

  methods: {
    // 当前可选资方和流量方
    getDict() {
      PPDqueryCapital().then(res => {
        this.bankChannelOptions = res.data || [];
      });
      PPDgetFlowConfig().then(res => {
        this.flowChannelOptions = res.data || [];
      });
    },
    tableRowStyle({ row, column, columnIndex }) {
      if (columnIndex === 8) return // 最后一列不设置样式
      if (row.enabled === "ENABLE") {
        return { opacity: "1" };
      } else if (row.enabled === "DISABLE") {
        return { opacity: ".5" };
      }
    },
    // 限额校验
    validateAmount(rule, value, callback) {
      if (value === "") {
        callback(new Error("请输入金额"));
      } else if (isNaN(value)) {
        callback(new Error("请输入有效的数字"));
      } else if (value > *********) {
        callback(new Error("金额不能超过 *********"));
      } else {
        const decimalRegex = /^\d+(\.\d{1,2})?$/;
        if (!decimalRegex.test(value)) {
          callback(new Error("最多允许两位小数"));
        } else {
          callback();
        }
      }
    },
    // 启用、关闭
    toggle(id, type) {
      this.$confirm(
        `是否确认 ${type === "ENABLE" ? "启用" : "停用"} 该资方?`,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          roundButton: true,
          type: "warning"
        }
      ).then(() => {
        PPDupdateEnabled({ id, enabled: type }).then(res => {
          this.getList();
        });
      });
    },
    // 获取列表
    getList() {
      this.loading = true;
      PPDqueryCapitalConfigPage(this.queryParams).then(res => {
        this.list = res.data.list
        this.total = res.data.total;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 新增 / 编辑 提交
    submit() {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          let periodsRange = this.ruleForm.periodsRange && Array.isArray(this.ruleForm.periodsRange) ? this.ruleForm.periodsRange : []
          periodsRange = periodsRange.sort((a, b) => a - b).join(",")
          let params = {
            ...this.ruleForm,
            periodsRange
          };
          PPDsaveCapitalConfig(params).then(res => {
            this.close();
            this.getList();
          });
        }
      });
    },
    // 新增弹窗
    async open() {

      this.visible = true;
      this.title = "新增";
      this.mode = "create";
    },

    // 关闭
    close() {
      this.visible = false;
      this.mode = "";
      this.title = "新增";
      this.$refs["ruleForm"].resetFields();
      this.ruleForm = {
        flowChannel: "", // 流量方
        bankChannel: "", // 资金方
        periodsRange: [], // 支持期数
        creditDayLimit: "", // 资方授信日限额
        loanDayLimit: "" // 资方放款日限额
      };
    },
    // 查看详情
    detail(id) {
      PPDgetCapitalConfig({ id }).then(res => {
        let data = res.data;
        const periodsRange = data.periodsRange && typeof data.periodsRange === 'string' ? data.periodsRange.split(',') : [] // 支持期数
        this.ruleForm = {
          ...data,
          periodsRange
        };

        this.mode = "detail";
        this.visible = true;
        this.title = "查看";
      });
    },
    // 修改
    edite(id) {
      PPDgetCapitalConfig({ id }).then(res => {
        let data = res.data;
        const periodsRange = data.periodsRange && typeof data.periodsRange === 'string' ? data.periodsRange.split(',') : [] // 支持期数
        this.ruleForm = {
          ...data,
          periodsRange
        };

        this.mode = "edite";
        this.visible = true;
        this.title = "修改";
      });
    }
  }
};
</script>
