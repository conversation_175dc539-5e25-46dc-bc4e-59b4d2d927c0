<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item>
        <el-select
          v-model="queryParams.type"
          placeholder="请选择"
          style="width: 100px;"
        >
          <el-option
            label="手机号"
            value="mobile"
          />
          <el-option
            label="订单编号"
            value="loanId"
          />
          <el-option
            label="身份证号"
            value="certNo"
          />
          <!-- <el-option label="放款渠道" value="certNo"></el-option> -->
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="queryParams.typeText"
          placeholder="请输入"
          clearable
          style="width:200px;"
          @keydown.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.flowChannel"
          placeholder="进件渠道"
          style="width: 160px;"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in dict.flowChannel"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dateArray"
          style="width: 320px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="pickerOptionsDefault"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          round
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          查询
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 列表 -->
    <el-table
      v-loading="loading"
      border="border"
      :data="list"
    >
      <el-table-column
        label="订单编号"
        align="center"
        fixed
        width="280"
      >
        <template slot-scope="scope">
          <el-link
            :underline="false"
            type="primary"
            @click="goPlan(scope.row)"
          >
            {{ scope.row.loanId || '-' }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="手机号"
        prop="mobile"
        align="center"
        width="120px"
      />
      <el-table-column
        label="期数"
        prop="periods"
        align="center"
        width="80px"
      />
      <el-table-column
        label="利率"
        prop="interestRate"
        align="center"
        width="100px"
      />
      <el-table-column
        label="审批金额"
        prop="approvedAmount"
        align="center"
      />
      <el-table-column
        label="放款金额"
        prop="loanAmount"
        align="center"
      />
      <el-table-column
        label="资金方"
        prop="funder"
        align="center"
      />
      <el-table-column
        label="进件渠道"
        prop="flowChannel"
        align="center"
        width="100px"
      >
        <template slot-scope="scope">
          <dict-tag
            :value="scope.row.flowChannel"
            :options="dict.flowChannel"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="订单状态"
        align="center"
      >
        <template slot-scope="scope">
          <dict-tag
            :value="scope.row.loanStatus || 'INIT'"
            :options="dict.orderState"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="申请日期"
        prop="applyDate"
        align="center"
        width="160px"
      />
      <el-table-column
        label="要款日期"
        prop="requestFundsDate"
        align="center"
        width="160px"
      />
      <el-table-column
        label="放款日期"
        prop="loanDate"
        align="center"
        width="160px"
      />
      <el-table-column label="结清证明">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isClear === 'Y'"
            v-permission="['admin','ppdai:download']"
            type="text"
            @click="handleDownload(scope.row, 'CREDIT_SETTLE_VOUCHER_FILE')"
          >
            下载
          </el-button>
        </template>
      </el-table-column>

      <el-table-column label="放款凭证">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isLoanState === 'Y'"
            v-permission="['admin','ppdai:download']"
            type="text"
            @click="handleDownload(scope.row, 'LOAN_VOUCHER_FILE')"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 放款列表
import { queryLoanPage, downloadFile } from "@/api/ppdai";
import crud from '@/mixins/crud'

export default {
  name: "PpdaiRRRR",
  mixins: [crud],
  data() {
    return {
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
        pageNum: 1,
        pageSize: 10,
        orderState: undefined,
        flowChannel: undefined,
        startTime: undefined,
        endTime: undefined,
        packageStatus: undefined,
        bankChannel:undefined,
      },
      loading: false,
      list: [],
      total: 0,
      // 字典
      orderStateOptions: [],
      bankChannelOptions:[],
      visible: false,
      fileList: [],
      dateArray: this.FormDataValue(),
      pickData: {},
      pickerOptionsDefault: {
        onPick: pick => {
          this.pickData = pick
        },
        disabledDate: time => {
          //设置当前时间后的时间不可选
          const now = new Date()
          const year = now.getFullYear()
          const mouth = now.getMonth() + 1
          const day = now.getDate()
          const {minDate, maxDate} = this.pickData
          const after =  time.valueOf() > new Date(`${year}-${mouth}-${day} 23:59:59`) // 是否今天之后
          if (minDate && !maxDate) {
            const interval = Math.abs(minDate.valueOf() - time.valueOf())
            const out31 = interval > 1000 * 3600 * 24 * 30 // 是否31天之外(time取了00:00:00, 23:59:59 作为一天的界限，所以这里用的是30天作为界限)
            return out31 || after;
          }
          return after;
        }
      },
    };
  },
  dicts: ["flowChannel", "orderState"],
  created() {
    this.getList()
  },
  methods: {
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    FormDataValue() {
      const start = new Date().getTime() - 3600 * 1000 * 24 * 30
      const end = new Date()
      const startDate = this.parseTime(start, '{y}-{m}-{d}')
      const endDate = this.parseTime(end, '{y}-{m}-{d}')

      return [`${startDate} 00:00:00`, `${endDate} 23:59:59`]
    },
    // 检查日期间隔
    checkInterval() {
      const interval = new Date(this.dateArray[1]).getTime() - new Date(this.dateArray[0]).getTime();
      if (interval > 0 && interval > (1000 * 60 * 60 * 24 * 31)){
        this.$message.error('请选择31天内的日期!')
        return true
      }
    },
    // 获取订单列表
    getList() {

      let params = {
        flowChannel: this.queryParams.flowChannel,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
      };
      if (this.queryParams.typeText) {
        params[this.queryParams.type] = this.queryParams.typeText
      }

      if(this.dateArray && this.dateArray.length === 2){
        params.startTime = this.dateArray[0]
        params.endTime = this.dateArray[1]
        // 判断时间差是否大于31天
        if (this.checkInterval()) return
      }else{
        params.startTime = undefined
        params.endTime = undefined
      }

      this.loading = true;
      queryLoanPage(params).then(res => {
        this.list = res.data.list;
        this.total = res.data.total;
        this.loading = false;
      }, error => {
        console.log(error)
        this.loading = false;
        this.$message.error('查询失败,请稍后再试!')
      })
    },
    // 还款计划
    goPlan(row) {
      this.$router.push(`/repaymentApply/index?loanId=${row.loanId || ''}`)
    },
    // 下载
    handleDownload(row, type = 'CREDIT_SETTLE_VOUCHER_FILE') {
      let params = {
        loanId: row.loanId,
        bankChannel: row.bankChannel,
        type
      }
      downloadFile(params).then(res => {
        const { fileStatus } = res.data

        if(fileStatus === 'PROCESSING'){
          this.$message.error('已请求资方结清证明，请1小时后查看')
        }else if(fileStatus === 'SUCCESS'){
          window.open(res.data.fileUrl)
        }
      }, error => {
        console.log(error)
        this.$message.error('下载失败,请稍后再试!')
      })
    }

  }
};
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: #e5e5e5 1px solid;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.item span {
  font-size: 16px;
}
</style>
