<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item
        label=""
        prop="mobile"
        width="150px"
      >
        <el-input
          v-model="queryParams.mobile"
          placeholder="手机号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        label=""
        prop="rightsSupplier"
        width="150px"
      >
        <el-input
          v-model="queryParams.rightsSupplier"
          placeholder="权益商编号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        label=""
        prop="repaymentNo"
        width="150px"
      >
        <el-input
          v-model="queryParams.repaymentNo"
          placeholder="扣款流水号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        label=""
        prop="businessNo"
        width="150px"
      >
        <el-input
          v-model="queryParams.businessNo"
          placeholder="业务单号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        label=""
        prop="paymentChannelCode"
        width="150px"
      >
        <el-select
          v-model="queryParams.paymentChannelCode"
          placeholder="扣款通道"
          style="width: 160px;"
          clearable
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="扣款时间">
        <el-date-picker
          v-model="rangeDate"
          size="small"
          type="datetimerange"
          style="width: 200px"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          round
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          round
          size="mini"
          @click="handleResetQuery"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      border="border"
      :data="dataSource"
    >
      <el-table-column
        label="扣款流水号"
        prop="repaymentNo"
        align="center"
      />
      <el-table-column
        label="业务单号"
        prop="businessNo"
        align="center"
      />
      <el-table-column
        label="姓名"
        prop="name"
        align="center"
      />
      <el-table-column
        label="手机号"
        prop="mobile"
        align="center"
      />
      <el-table-column
        label="权益供应商"
        prop="rightsSupplier"
        align="center"
        :formatter="rightsPayChannelFormat"
      />
      <el-table-column
        label="权益金额（元）"
        prop="rightsAmount"
        align="center"
      />
      <el-table-column
        label="实际金额"
        prop="realAmount"
        align="center"
      />
      <el-table-column
        label="放款时间"
        prop="loanTime"
        align="center"
      />
      <el-table-column
        label="扣款通道"
        prop="paymentChannelName"
        align="center"
      />
      <el-table-column
        label="扣款时间"
        prop="repaymentTime"
        align="center"
      />
      <el-table-column
        label="扣款失败原因"
        prop="failReason"
        align="center"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="handleSearchList"
    />
  </div>
</template>

<script>
import { get as getDictByName } from '@/api/system/dictDetail'
import {
  queryRepayRecordList
} from '@/api/rights'

const initState = {
  pageNum: 1,
  pageSize: 10,
  mobile: undefined,
  rightsSupplier: undefined,
  repaymentNo: undefined,
  businessNo: undefined,
  paymentChannelCode: undefined
}

export default {
  name: '',
  data() {
    return {
      queryParams: {
        pageNum: initState.pageNum,
        pageSize: initState.pageSize,
        mobile: initState.mobile,
        rightsSupplier: initState.rightsSupplier,
        repaymentNo: initState.repaymentNo,
        businessNo: initState.businessNo,
        paymentChannelCode: initState.paymentChannelCode
      },
      rangeDate: [],
      total: 0,

      loading: true,
      dataSource: [],
      channelOptions: [],
      rightsSupplierOptions: []
    }
  },
  created() {
    this.handleSearchList()

    getDictByName('payChannel').then(res => {
      this.channelOptions = res.content
    })

    getDictByName('rightsSupplier').then(res => {
        this.rightsSupplierOptions = res.content
      })
  },
  methods: {
    // 获取列表
    async handleSearchList() {
      this.loading = true

      const params = {
        ...this.queryParams,
        startTime: this.rangeDate.length ? this.rangeDate[0] : undefined,
        endTime: this.rangeDate.length ? this.rangeDate[1] : undefined
      }
      const result = await queryRepayRecordList(params).finally(() => {
        this.loading = false
      })



      if (result) {
        this.dataSource = result.data.list
        this.total = result.data.total
      }
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.handleSearchList()
    },
    handleResetQuery() {
      this.queryParams = {
        ...initState
      }

      this.rangeDate = []

      this.handleSearchList()
    },
    rightsPayChannelFormat({ rightsSupplier }) {
      const obj = this.rightsSupplierOptions.find(item => item.value === rightsSupplier)
      return obj ? obj.label : '--'
    }
  }
}
</script>

<style scoped>
.empty {
  font-size: 30px;
  color: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 500px;
}

.box {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.box .label {
  margin-right: 16px;
}

.tip {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
}

.table-head {
  background: #f5f5f5;
  display: flex;
  align-items: center;
}

.table-head span {
  padding: 10px;
  font-size: 13px;
}

.table-head span:nth-child(1) {
  width: 20%;
}

.table-head span:nth-child(2) {
  width: 50%;
}

.table-head span:nth-child(3) {
  flex: 1;
}

.table-body .item {
  display: flex;
  align-items: center;
  border-bottom: #e5e5e5 1px solid;
  background: #fff;
}

.table-body .item:hover {
  background: #e5e5e5;
}

.table-body .item span:nth-child(1) {
  width: 20%;
  padding: 10px;
}

.table-body .item span:nth-child(2) {
  width: 50%;
  padding: 10px;
}

.item-btns {
  padding: 5px 10px;
  flex: 1;
  display: flex;
  align-items: center;
}

.new-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-bottom: #e5e5e5 1px solid;
  position: relative;
}

.drop {
  background: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 240px;
  top: 30px;
  padding: 6px 16px;
  border-radius: 10px;
  width: 180px;
}

.drop-list {
  padding: 5px 0;
}

.btn-box {
  display: flex;
  justify-content: flex-end;
  padding: 6px 0 6px 0;
}

.dis-tip {
  font-style: normal;
  font-size: 12px;
  background: #f5f5f5;
  display: inline-block;
  padding: 4px 4px;
  border-radius: 4px;
  border: #e5e5e5 1px solid;
  line-height: 1;
  margin-left: 8px;
}
</style>
