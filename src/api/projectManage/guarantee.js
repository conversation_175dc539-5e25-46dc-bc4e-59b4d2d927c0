import request from '@/utils/request'

// 主列表查询
export function getList(data) {
  return request({
    url: 'api/guaranteeConfig/queryGuaranteeConfig',
    method: 'get',
    params: data
  })
}

// 新增资产方
export function addAsset(data) {
  return request({
    url: 'api/guaranteeConfig/createGuaranteeConfig',
    method: 'post',
    data
  })
}

// 启用/停用
export function updateStatus(data) {
  return request({
    url: 'api/guaranteeConfig/enable',
    method: 'post',
    data
  })
}

// 详情查询
export function getDetail(data) {
  return request({
    url: 'api/guaranteeConfig/info',
    method: 'get',
    params: data
  })
}

// 修改
export function updateAsset(data) {
  return request({
    url: 'api/guaranteeConfig/updateGuaranteeConfig',
    method: 'post',
    data
  })
}
