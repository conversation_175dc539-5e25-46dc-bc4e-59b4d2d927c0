<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.data.QhDataCustomRepayRecordMapper">
    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.data.QhCustomRepayRecord">
        <result column="id" property="id"/>
        <result column="outer_repay_no" property="outerRepayNo"/>
        <result column="loan_id" property="loanId"/>
        <result column="period" property="period"/>
        <result column="repay_apply_date" property="repayApplyDate"/>
        <result column="repay_purpose" property="repayPurpose"/>
        <result column="repay_mode" property="repayMode"/>
        <result column="principal_amt" property="principalAmt"/>
        <result column="interest_amt" property="interestAmt"/>
        <result column="guarantee_amt" property="guaranteeAmt"/>
        <result column="capital_guarantee_amt" property="capitalGuaranteeAmt"/>
        <result column="penalty_amt" property="penaltyAmt"/>
        <result column="capital_penalty_amt" property="capitalPenaltyAmt"/>
        <result column="breach_amt" property="breachAmt"/>
        <result column="reduce_amount" property="reduceAmount"/>
        <result column="total_amt" property="totalAmt"/>
        <result column="agreement_no" property="agreementNo"/>
        <result column="repay_state" property="repayState"/>
        <result column="fail_reason" property="failReason"/>
        <result column="repaid_date" property="repaidDate"/>
        <result column="need_twice_state" property="needTwiceState"/>
        <result column="twice_repaid_date" property="twiceRepaidDate"/>
        <result column="remark" property="remark"/>
        <result column="revision" property="revision"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="pay_side" property="paySide"/>
        <result column="operation_source" property="operationSource"/>
        <result column="extra_guarantee_amt" property="extraGuaranteeAmt"/>
        <result column="consult_fee" property="consultFee"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        outer_repay_no,
        loan_id,
        period,
        repay_apply_date,
        repay_purpose,
        repay_mode,
        principal_amt,
        interest_amt,
        guarantee_amt,
        capital_guarantee_amt,
        penalty_amt,
        capital_penalty_amt,
        breach_amt,
        reduce_amount,
        total_amt,
        agreement_no,
        repay_state,
        fail_reason,
        repaid_date,
        need_twice_state,
        twice_repaid_date,
        remark,
        revision,
        created_by,
        created_time,
        updated_by,
        updated_time,
        pay_side,
        operation_source,
        extra_guarantee_amt,
        consult_fee
    </sql>


    <select id="selectByLoanIds" resultType="com.jinghang.cash.modules.manage.Job.bo.QhCustomRepayRecordBo">
        SELECT r.loan_id     AS loanId,
               r.repay_state AS repayState,
               r.fail_reason AS failReason,
               r.repaid_date AS repaidDate
        FROM
        s03_qh_custom_repay_record r
            JOIN ( -- 子查询：获取每个 loan_id 的最大时间
        SELECT loan_id, MAX(repay_apply_date) AS max_repay_apply_date FROM s03_qh_custom_repay_record WHERE loan_id IN
        <foreach collection="loanIds" item="loanId" open="(" separator="," close=")">
            #{loanId}
        </foreach>
        GROUP BY loan_id) AS latest ON r.loan_id = latest.loan_id
            AND r.repay_apply_date = latest.max_repay_apply_date -- 精准匹配时间
    </select>
</mapper>
