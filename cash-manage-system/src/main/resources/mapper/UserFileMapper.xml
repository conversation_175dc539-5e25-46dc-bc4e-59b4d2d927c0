<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.UserFileMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.UserFile">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="loanStage" column="loan_stage" jdbcType="VARCHAR"/>
            <result property="fileType" column="file_type" jdbcType="VARCHAR"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="ossBucket" column="oss_bucket" jdbcType="VARCHAR"/>
            <result property="ossKey" column="oss_key" jdbcType="VARCHAR"/>
            <result property="signFinal" column="sign_final" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="revision" column="revision" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,loan_stage,
        file_type,file_name,oss_bucket,
        oss_key,sign_final,remark,
        revision,created_by,created_time,
        updated_by,updated_time
    </sql>

    <!-- 查询用户附件信息 -->
    <select id="queryUserFileInfo" resultType="com.jinghang.cash.pojo.UserFile">
        select
        <include refid="Base_Column_List" />
        from user_file where user_id =#{userId} and file_type = 'ID_FACE' and loan_stage = 'RISK';
    </select>

    <!--查询用户签章协议信息-->
    <select id="queryUserAgreementFile" resultType="com.jinghang.cash.pojo.UserFile">
        select
        <include refid="Base_Column_List" />
        from user_file where id in
        <foreach collection="userFileIds" open="(" separator="," close=")" item="userFileId">
            #{userFileId}
        </foreach>
    </select>

</mapper>
