<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.RightsConfigMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.RightsConfig">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="riskLevel" column="risk_level" jdbcType="INTEGER"/>
        <result property="approveRightsForce" column="approve_rights_force" jdbcType="VARCHAR"/>
        <result property="sellingPrice" column="selling_price" jdbcType="DECIMAL"/>
        <result property="rightsPackageId" column="rights_package_id" jdbcType="VARCHAR"/>
        <result property="useStatus" column="use_status" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="flowChannel" column="flow_channel" jdbcType="VARCHAR"></result>
    </resultMap>

    <sql id="Base_Column_List">
        id,risk_level,bottom,
        approve_rights,rights_type,approve_rights_force,
        sellingPrice,remark,revision,
        created_by,created_time,updated_by,flow_channel
        updated_time
    </sql>
    <select id="queryRightsPage" resultType="com.jinghang.cash.modules.manage.vo.res.RightsBasePackageResponse">
        select a.id,
               a.risk_level,
               b.package_name,
               b.costing_price,
               b.code,
               a.flow_channel,
               a.selling_price,
               a.approve_rights_force,
               a.updated_time,
               a.updated_by,
               a.use_status
        from rights_config a
                 left join rights_base_package b on a.rights_package_id = b.id
        order by a.updated_time desc
    </select>
</mapper>
