<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.LoanMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.Loan">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="creditId" column="credit_id" jdbcType="VARCHAR"/>
        <result property="loanRecordId" column="loan_record_id" jdbcType="VARCHAR"/>
        <result property="flowChannel" column="flow_channel" jdbcType="VARCHAR"/>
        <result property="outerLoanId" column="outer_loan_id" jdbcType="VARCHAR"/>
        <result property="applyTime" column="apply_time" jdbcType="TIMESTAMP"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="periods" column="periods" jdbcType="INTEGER"/>
        <result property="loanPurpose" column="loan_purpose" jdbcType="VARCHAR"/>
        <result property="loanCardId" column="loan_card_id" jdbcType="VARCHAR"/>
        <result property="repayCardId" column="repay_card_id" jdbcType="VARCHAR"/>
        <result property="packageId" column="package_id" jdbcType="VARCHAR"/>
        <result property="loanState" column="loan_state" jdbcType="VARCHAR"/>
        <result property="bankChannel" column="bank_channel" jdbcType="VARCHAR"/>
        <result property="loanNo" column="loan_no" jdbcType="VARCHAR"/>
        <result property="loanTime" column="loan_time" jdbcType="TIMESTAMP"/>
        <result property="planSyncCore" column="plan_sync_core" jdbcType="VARCHAR"/>
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
        <result property="loanContractNo" column="loan_contract_no" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="bankRate" column="bank_rate" jdbcType="DECIMAL"/>
        <result property="irrRate" column="irr_rate" jdbcType="DECIMAL"/>
        <result property="rightsDeductState" column="rights_deduct_state" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,order_id,
        credit_id,loan_record_id,flow_channel,outer_loan_id,
        apply_time,amount,periods,
        loan_purpose,loan_card_id,repay_card_id,
        package_id,loan_state,bank_channel,
        loan_no,loan_time,plan_sync_core,
        fail_reason,loan_contract_no,remark,
        revision,created_by,created_time,
        updated_by,updated_time,bank_rate,
        irr_rate,rights_deduct_state
    </sql>

    <select id="selectByOrderId" resultType="com.jinghang.cash.pojo.Loan">
        select  * from loan where order_id = #{orderId} and loan_state = 'SUCCEED'
    </select>

    <select id="findByOrderId" resultType="com.jinghang.cash.pojo.Loan">
        select <include refid="Base_Column_List"/> from loan where order_id = #{orderId}
    </select>
</mapper>
