<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.CustomRepayRecordMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.CustomRepayRecord">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="outerRepayNo" column="outer_repay_no" jdbcType="VARCHAR"/>
            <result property="loanId" column="loan_id" jdbcType="VARCHAR"/>
            <result property="period" column="period" jdbcType="INTEGER"/>
            <result property="repayApplyDate" column="repay_apply_date" jdbcType="TIMESTAMP"/>
            <result property="repayPurpose" column="repay_purpose" jdbcType="VARCHAR"/>
            <result property="repayMode" column="repay_mode" jdbcType="VARCHAR"/>
            <result property="principalAmt" column="principal_amt" jdbcType="DECIMAL"/>
            <result property="interestAmt" column="interest_amt" jdbcType="DECIMAL"/>
            <result property="guaranteeAmt" column="guarantee_amt" jdbcType="DECIMAL"/>
            <result property="penaltyAmt" column="penalty_amt" jdbcType="DECIMAL"/>
            <result property="breachAmt" column="breach_amt" jdbcType="DECIMAL"/>
            <result property="totalAmt" column="total_amt" jdbcType="DECIMAL"/>
            <result property="agreementNo" column="agreement_no" jdbcType="VARCHAR"/>
            <result property="repayState" column="repay_state" jdbcType="VARCHAR"/>
            <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
            <result property="repaidDate" column="repaid_date" jdbcType="TIMESTAMP"/>
            <result property="needTwiceState" column="need_twice_state" jdbcType="VARCHAR"/>
            <result property="twiceRepaidDate" column="twice_repaid_date" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="revision" column="revision" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="paySide" column="pay_side" jdbcType="VARCHAR"/>
            <result property="operationSource" column="operation_source" jdbcType="VARCHAR"/>
            <result property="extraGuaranteeAmt" column="extra_guarantee_amt" jdbcType="DECIMAL"/>
            <result property="consultFee" column="consult_fee" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,outer_repay_no,loan_id,
        period,repay_apply_date,repay_purpose,
        repay_mode,principal_amt,interest_amt,
        guarantee_amt,penalty_amt,breach_amt,
        total_amt,agreement_no,repay_state,
        fail_reason,repaid_date,need_twice_state,
        twice_repaid_date,remark,revision,
        created_by,created_time,updated_by,
        updated_time,pay_side,operation_source,
        extra_guarantee_amt,consult_fee
    </sql>
    <select id="findLatestFailRepayRecord" resultType="com.jinghang.cash.pojo.CustomRepayRecord">
        select * from custom_repay_record where loan_id = #{loanId}
        and period = #{period}
        and repay_state = 'FAILED' order by created_time desc limit 1
    </select>
</mapper>
