package com.jinghang.cash.convert;


import com.jinghang.cash.modules.manage.vo.req.ManagePusUpRequest;
import com.jinghang.cash.modules.manage.vo.req.ManagePushUpdateRequest;
import com.jinghang.cash.modules.manage.vo.res.ManageAppPushConfigQueryResponse;
import com.jinghang.cash.pojo.AppPushConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> gale
 * @Classname AppPushConfigConvert
 * @Description TODO
 * @Date 2024/3/22 15:09
 */
@Mapper
public interface AppPushConfigConvert {

    AppPushConfigConvert INSTANCE = Mappers.getMapper(AppPushConfigConvert.class);

    AppPushConfig toAppPushConfig(ManagePusUpRequest request);

    @Mapping(source = "id", target = "clientNumId")
    ManageAppPushConfigQueryResponse toManageAppPushConfigQueryResponse(AppPushConfig appPushConfig);

    @Mapping(source = "clientNumId", target = "id")
    AppPushConfig toAppPushConfig(ManagePushUpdateRequest request);

}
