package com.jinghang.cash.pojo.data;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 对客还款记录
 */
@Data
@TableName(value = "s03_qh_custom_repay_record")
public class QhCustomRepayRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 外部还款流水号
     */
    private String outerRepayNo;

    /**
     * 借据
     */
    private String loanId;

    /**
     * 期次
     */
    private Integer period;

    /**
     * 申请日期
     */
    private Date repayApplyDate;

    /**
     * 还款目的
     */
    private String repayPurpose;

    /**
     * 还款模式，线上线下
     */
    private String repayMode;

    /**
     * 本金
     */
    private BigDecimal principalAmt;

    /**
     * 利息（银行）
     */
    private BigDecimal interestAmt;

    /**
     * 融担费
     */
    private BigDecimal guaranteeAmt;

    /**
     * 资方应收融担费
     */
    private BigDecimal capitalGuaranteeAmt;

    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;

    /**
     * 资方罚息
     */
    private BigDecimal capitalPenaltyAmt;

    /**
     * 违约金
     */
    private BigDecimal breachAmt;

    /**
     * 减免金额
     */
    private BigDecimal reduceAmount;

    /**
     * 还款总额
     */
    private BigDecimal totalAmt;

    /**
     * 代扣协议号
     */
    private String agreementNo;

    /**
     * 还款状态
     */
    private String repayState;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 还款成功时间
     */
    private Date repaidDate;

    /**
     * 是否需要二次扣款
     */
    private String needTwiceState;

    /**
     * 二次扣款成功时间
     */
    private Date twiceRepaidDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private String revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 扣款方
     */
    private String paySide;

    /**
     * 还款操作人
     */
    private String operationSource;

    /**
     * 额外的融担费
     */
    private BigDecimal extraGuaranteeAmt;

    /**
     * 咨询费
     */
    private BigDecimal consultFee;
}
