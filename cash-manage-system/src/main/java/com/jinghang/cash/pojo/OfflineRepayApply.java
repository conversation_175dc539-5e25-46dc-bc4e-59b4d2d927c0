package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.enums.ProcessState;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线下还款申请
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "offline_repay_apply")
public class OfflineRepayApply implements Serializable {

    private static final long serialVersionUID = 786028457344626743L;

    @TableId
    private String id;
    /**
     * 订单号
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 借据号
     */
    @TableField(value = "loan_id")
    private String loanId;
    /**
     * 期数
     */
    @TableField(value = "period")
    private Integer period;
    /**
     * 还款模式
     */
    @TableField(value = "repay_purpose")
    private String repayPurpose;
    /**
     * 申请时间
     */
    @TableField(value = "apply_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applyTime;
    /**
     * 应还本金
     */
    @TableField(value = "principal_amt")
    private BigDecimal principalAmt;
    /**
     * 应还利息
     */
    @TableField(value = "interest_amt")
    private BigDecimal interestAmt;
    /**
     * 应还担保费
     */
    @TableField(value = "guarantee_amt")
    private BigDecimal guaranteeAmt;
    /**
     * 应还罚息
     */
    @TableField(value = "penalty_amt")
    private BigDecimal penaltyAmt;
    /**
     * 应还咨询费
     */
    @TableField(value = "consult_fee")
    private BigDecimal consultFee;
    /**
     * 应还总金额
     */
    @TableField(value = "amount")
    private BigDecimal amount;
    /**
     * 减免金额
     */
    @TableField(value = "reduce_amount")
    private BigDecimal reduceAmount;
    /**
     * 实还本金
     */
    @TableField(value = "act_principal_amt")
    private BigDecimal actPrincipalAmt;
    /**
     * 实还利息
     */
    @TableField(value = "act_interest_amt")
    private BigDecimal actInterestAmt;
    /**
     * 实还担保费
     */
    @TableField(value = "act_guarantee_amt")
    private BigDecimal actGuaranteeAmt;
    /**
     * 实还罚息
     */
    @TableField(value = "act_penalty_amt")
    private BigDecimal actPenaltyAmt;
    /**
     * 实还咨询费
     */
    @TableField(value = "act_consult_fee")
    private BigDecimal actConsultFee;
    /**
     * 实还总金额
     */
    @TableField(value = "act_amount")
    private BigDecimal actAmount;
    /**
     * 销账状态
     */
    @TableField(value = "apply_state")
    private ProcessState applyState;

    /**
     * 溢出金额
     */
    @TableField(value = "overflow_amount")
    private BigDecimal overflowAmount;

    /**
     * 备注
     */
    @TableField(value = "remark", fill = FieldFill.INSERT)
    private String remark;
    /**
     * 版本号
     */
    @TableField(value = "revision", fill = FieldFill.INSERT)
    @Version
    private String revision;
    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

}
