package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 额度策略配置表
 */
@Data
@TableName("revolving_strategy_config")
public class RevolvingStrategyConfig implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 2193422579956456806L;

    @TableId
    private String id;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略编码
     */
    private String strategyCode;

    /**
     * 额度区间上限
     */
    private Integer amountUpper;

    /**
     * 额度区间下限
     */
    private Integer amountLower;

    /**
     * 策略状态（启用/禁用）
     */
    private String strategyState;

    /**
     * 备注
     */
    private String remark;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
