package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * @TableName user_risk_record
 */
@TableName(value ="user_risk_record")
@Data
public class UserRiskRecord implements Serializable {
    private String id;

    private String userId;

    private String flowChannel;

    private String ruleCode;

    private String ruleDesc;

    private String approveResultCode;

    private String approveResult;

    private BigDecimal approveAmount;

    private String approveRights;

    private String riskFinalResult;

    private Date passTime;

    private Date expireTime;

    private String remark;

    private String revision;

    private String createdBy;

    private LocalDateTime createdTime;

    private String updatedBy;

    private LocalDateTime updatedTime;

    private Integer riskLevel;

    private static final long serialVersionUID = 1L;
}
