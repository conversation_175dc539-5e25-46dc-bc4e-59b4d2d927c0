package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.cash.enums.PushType;
import com.jinghang.cash.enums.VersionType;
import com.jinghang.cash.mapper.BaseEntity;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * @TableName app_push_config_relation
 */
@TableName(value = "app_push_config_relation")
@Data
public class AppPushConfigRelation extends BaseEntity {



    private String channelConfigId;

    private String pushConfigId;

    @Enumerated(EnumType.STRING)
    private VersionType versionType;

    @Enumerated(EnumType.STRING)
    private PushType pushType;


}
