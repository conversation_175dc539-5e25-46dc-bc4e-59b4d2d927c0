package com.jinghang.cash.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.pojo.DrainageStatistics;
import com.jinghang.cash.service.DrainageStatisticsService;
import com.jinghang.cash.mapper.DrainageStatisticsMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【drainage_statistics(引流统计表)】的数据库操作Service实现
* @createDate 2023-11-15 15:27:45
*/
@Service
public class DrainageStatisticsServiceImpl extends ServiceImpl<DrainageStatisticsMapper, DrainageStatistics>
    implements DrainageStatisticsService{

}




