package com.jinghang.cash.modules.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyRelateQueryRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyRelateSaveRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigDetailRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigPagingRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigSaveRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigStatusRequest;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyRelateResponse;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyConfigResponse;
import com.jinghang.cash.pojo.RevolvingStrategyConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface RevolvingStrategyConfigService extends IService<RevolvingStrategyConfig> {

    PageInfo<RevolvingStrategyConfigResponse> paging(RevolvingStrategyConfigPagingRequest request);

    void save(RevolvingStrategyConfigSaveRequest request);

    RevolvingStrategyConfigResponse detail(RevolvingStrategyConfigDetailRequest request);

    void enable(RevolvingStrategyConfigStatusRequest request);

    void disable(RevolvingStrategyConfigStatusRequest request);

    void relateSave(RevolvingStrategyRelateSaveRequest request);

    List<RevolvingStrategyRelateResponse> relateQuery(RevolvingStrategyRelateQueryRequest request);
}
