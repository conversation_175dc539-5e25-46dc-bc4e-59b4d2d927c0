/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.mapper;

import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.modules.project.domain.ProjectElements;
import com.jinghang.cash.modules.project.domain.dto.ProjectElementsQueryCriteria;

import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
* <AUTHOR>
* @date 2025-08-25
**/
@Mapper
public interface ProjectElementsMapper extends BaseMapper<ProjectElements> {


    /**
     * 根据项目编码查询项目要素
     *
     * @param projectCode 项目编码
     * @return 项目要素
     */
    ProjectElements selectByProjectCode(@Param("projectCode") String projectCode);

    /**
     * 根据项目编码查询启用状态的项目要素，按时效类型排序（临时优先）
     *
     * @param projectCode 项目编码
     * @param enabled     启用状态
     * @return 项目要素列表
     */
    List<ProjectElements> selectByProjectCodeAndEnabledOrderByProjectDurationTypeDesc(
            @Param("projectCode") String projectCode,
            @Param("enabled") AbleStatus enabled);

    /**
     * 根据项目编码查询有效的临时项目要素
     *
     * @param projectCode  项目编码
     * @param enabled      启用状态
     * @param durationType 时效类型
     * @param currentTime  当前时间
     * @return 项目要素
     */
    ProjectElements selectValidTemporaryElements(
            @Param("projectCode") String projectCode,
            @Param("enabled") AbleStatus enabled,
            @Param("durationType") String durationType,
            @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据项目编码查询长期项目要素
     *
     * @param projectCode  项目编码
     * @param enabled      启用状态
     * @param durationType 时效类型
     * @return 项目要素
     */
    ProjectElements selectByProjectCodeAndEnabledAndProjectDurationType(
            @Param("projectCode") String projectCode,
            @Param("enabled") AbleStatus enabled,
            @Param("durationType") String durationType);


    IPage<ProjectElements> findAll(@Param("criteria") ProjectElementsQueryCriteria criteria, Page<Object> page);

    List<ProjectElements> findAll(@Param("criteria") ProjectElementsQueryCriteria criteria);
}