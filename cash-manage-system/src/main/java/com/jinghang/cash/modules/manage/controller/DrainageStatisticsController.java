package com.jinghang.cash.modules.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.annotation.Log;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.service.DrainageStatisticsDataService;
import com.jinghang.cash.modules.manage.vo.req.DrainageStatisticsRequest;
import com.jinghang.cash.pojo.DrainageStatistics;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> gale
 * @Classname DrainageStatisticsController
 * @Description 引流统计
 * @Date 2023/11/20 16:50
 */
@RestController
@RequestMapping("/drainage")
public class DrainageStatisticsController {

    @Autowired
    private DrainageStatisticsDataService drainageStatisticsDataService;

    @Log("引流统计详情查询")
    @PostMapping(value = "/query")
    @ApiOperation("引流统计详情查询")
    public RestResult<PageInfo<DrainageStatistics>> query(@RequestBody DrainageStatisticsRequest request) {
        Page<DrainageStatistics> page = drainageStatisticsDataService.queryAll(request);
        return RestResult.success(new PageInfo<>(page));
    }


    @ApiOperation("导出引流详情")
    @GetMapping(value = "/download")
//    @PreAuthorize("@el.check('rights:query')")
    public void exportJob(HttpServletResponse response, DrainageStatisticsRequest request) throws IOException {
        Page<DrainageStatistics> page = drainageStatisticsDataService.queryAll(request);
        drainageStatisticsDataService.download(page.getResult(), response);
    }

}
