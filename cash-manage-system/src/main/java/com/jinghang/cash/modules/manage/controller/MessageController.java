package com.jinghang.cash.modules.manage.controller;

import com.jinghang.cash.enums.ResultCode;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.service.MessageService;
import com.jinghang.cash.modules.manage.vo.req.UserOrderReq;
import com.jinghang.cash.pojo.MessageSendRecord;
import com.jinghang.cash.pojo.MessageTemplate;
import com.jinghang.ppd.api.dto.SmsSendReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 短信
 */
@RestController
@RequestMapping("/sms")
public class MessageController {

    @Autowired
    private MessageService messageService;

    /**
     * 短信记录查询
     */
    @PostMapping(value = "/queryRecord")
    public RestResult<Object> queryMessageSendRecord(@Validated @RequestBody UserOrderReq userOrderReq) {
        List<MessageSendRecord> messageSendRecords = messageService.queryMessageSendRecord(userOrderReq);
        return RestResult.success(messageSendRecords);
    }

    /**
     * 短信模板查询
     */
    @PostMapping(value = "/queryTemplate")
    public RestResult<Object> queryMessageTemplate() {
        List<MessageTemplate> messageTemplateList = messageService.queryMessageTemplate();
        return RestResult.success(messageTemplateList);
    }

    /**
     * 发送短信
     */
    @PostMapping(value = "/sendMessage")
    public RestResult<Object> sendMessage(@Validated @RequestBody SmsSendReq smsSendReq) {
        Boolean b = messageService.sendMessage(smsSendReq);
        if (!b) {
            return RestResult.fail(ResultCode.BIZ_ERROR, ResultCode.BIZ_ERROR.getMsg());
        }
        return RestResult.success(ResultCode.SUCCESS);
    }


}
