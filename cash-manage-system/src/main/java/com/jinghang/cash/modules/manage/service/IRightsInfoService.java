package com.jinghang.cash.modules.manage.service;

import com.jinghang.cash.modules.manage.vo.req.RightsInfoQueryRequest;
import com.jinghang.cash.modules.manage.vo.res.RightInfoResponse;
import com.jinghang.cash.modules.manage.vo.res.RightsInfoQueryResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @Classname IRightsInfoService
 * @Description 权益包服务类
 * @Date 2023/11/15 20:17
 * @Created by gale
 */
public interface IRightsInfoService {

    List<RightsInfoQueryResponse> queryAll(RightsInfoQueryRequest request);

    RightInfoResponse rightInfo(RightsInfoQueryRequest request);


    void download(List<RightsInfoQueryResponse> rightsInfoVos, HttpServletResponse response) throws IOException;
}
