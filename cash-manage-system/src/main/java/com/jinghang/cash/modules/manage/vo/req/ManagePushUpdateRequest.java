package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.enums.WhetherState;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR> gale
 * @Classname ManagePushUpdateRequest
 * @Description 请求更新
 * @Date 2024/3/23 16:58
 */
@Data
public class ManagePushUpdateRequest {


    private String versionName;

    private String versionNum;

    @Enumerated(EnumType.STRING)
    private WhetherState pushType;

    @Enumerated(EnumType.STRING)
    private AbleStatus pushEnable;

    private String pushContext;

    private String pushUrl;

    private String clientNum;

    @NotBlank
    private String clientNumId;

    private List<String> channelList;

}
