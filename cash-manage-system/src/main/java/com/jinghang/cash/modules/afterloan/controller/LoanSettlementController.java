package com.jinghang.cash.modules.afterloan.controller;

import com.jinghang.cash.modules.afterloan.service.LoanSettlementService;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.vo.req.TrialReq;
import com.jinghang.cash.modules.manage.vo.rsp.TrialRes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结清试算
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/loanSettlement")
public class LoanSettlementController {

    @Autowired
    private LoanSettlementService loanSettlementService;

    /**
     * 试算
     *
     * @return
     */
    @PostMapping("/trial")
    public RestResult<TrialRes> settlementTrial(@RequestBody @Validated TrialReq req) {
        TrialRes res = loanSettlementService.trial(req);
        return RestResult.success(res);
    }
}

