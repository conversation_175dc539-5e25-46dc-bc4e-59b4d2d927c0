package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.enums.RightsSupplier;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
public class RightsRouteConfigDetailRequest {

    @NotNull(message = "供应商不能为空")
    @Enumerated(EnumType.STRING)
    private RightsSupplier rightsSupplier;
}
