/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.mapper;

import com.jinghang.cash.modules.project.domain.ProjectElementsExt;
import com.jinghang.cash.modules.project.domain.dto.ProjectElementsExtQueryCriteria;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
* <AUTHOR>
* @date 2025-08-25
**/
@Mapper
public interface ProjectElementsExtMapper extends BaseMapper<ProjectElementsExt> {

    IPage<ProjectElementsExt> findAll(@Param("criteria") ProjectElementsExtQueryCriteria criteria, Page<Object> page);

    List<ProjectElementsExt> findAll(@Param("criteria") ProjectElementsExtQueryCriteria criteria);

    /**
     * 根据项目编码查询项目要素扩展
     *
     * @param projectCode 项目编码
     * @return 项目要素扩展
     */
    ProjectElementsExt selectByProjectCode(@Param("projectCode") String projectCode);

    /**
     * 根据parentId查询项目要素扩展
     *
     * @param parentId 上级要素ID
     * @return 项目要素扩展
     */
    ProjectElementsExt selectByParentId(@Param("parentId") String parentId);
}