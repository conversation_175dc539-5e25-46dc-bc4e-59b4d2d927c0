package com.jinghang.cash.modules.manage.controller;

import com.github.pagehelper.PageInfo;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.service.FlowRouteConfigService;
import com.jinghang.cash.modules.manage.vo.PageParam;
import com.jinghang.cash.modules.manage.vo.req.CapitalConfigRequest;
import com.jinghang.cash.modules.manage.vo.req.EnableCapitalConfigReq;
import com.jinghang.cash.modules.manage.vo.req.EnableFlowRouteConfigReq;
import com.jinghang.cash.modules.manage.vo.req.FlowChannelReq;
import com.jinghang.cash.modules.manage.vo.req.FlowProtocolChannelDetailReq;
import com.jinghang.cash.modules.manage.vo.req.FlowRouteConfigRequest;
import com.jinghang.cash.modules.manage.vo.res.BankChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigInfoResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowProtocolChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigInfoResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigPageResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资金流量相关接口
 * ********
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/capitalFlow")
@Api(tags = "资方渠道配置接口")
public class CapitalFlowController {

    @Autowired
    private FlowRouteConfigService flowRouteConfigService;

    /**
     * 轻管后台-资方配置列表
     */
    @PostMapping("/queryCapitalConfigPage")
    public RestResult<PageInfo<CapitalConfigResponse>> queryCapitalFlowPage(@Validated @RequestBody PageParam reqVO) {
        PageInfo<CapitalConfigResponse> result = flowRouteConfigService.queryCapitalFlowPage(reqVO);
        return RestResult.success(result);
    }

    /**
     * 轻管后台-资方配置修改
     */
    @PostMapping("/updateCapitalConfig")
    public RestResult<Void> updateCapitalConfig(@Validated @RequestBody CapitalConfigRequest reqVO) {
        flowRouteConfigService.updateCapitalConfig(reqVO);
        return RestResult.success();
    }

    /**
     * 轻管后台-资方配置新增
     */
    @PostMapping("/saveCapitalConfig")
    public RestResult<Void> saveCapitalConfig(@Validated @RequestBody CapitalConfigRequest reqVO) {
        flowRouteConfigService.saveCapitalConfig(reqVO);
        return RestResult.success();
    }

    /**
     * 轻管后台-资方配置启用禁用
     */
    @PostMapping("/enableCapitalConfig")
    public RestResult<Void> enableCapitalConfig(@Validated @RequestBody EnableCapitalConfigReq reqVO) {
        flowRouteConfigService.enableCapitalConfig(reqVO);
        return RestResult.success();
    }

    /**
     * 轻管后台- 资方配置获取去详情
     */
    @GetMapping("/getCapitalConfig")
    public RestResult<CapitalConfigInfoResponse> getCapitalConfig(String id) {
        CapitalConfigInfoResponse result = flowRouteConfigService.getCapitalConfig(id);
        return RestResult.success(result);
    }

    /**
     * 轻管后台 - 资方配置获取所有可选择资方
     */
    @GetMapping("/getCapitalList")
    public RestResult<List<BankChannelResponse>> getCapitalList() {
        List<BankChannelResponse> result = flowRouteConfigService.getCapitalList();
        return RestResult.success(result);
    }

    /**
     * 轻管后台 - 资方配置新增时,获取所有可选择资方
     */
    @GetMapping("/queryCapital")
    public RestResult<List<BankChannelResponse>> queryCapital() {
        List<BankChannelResponse> result = flowRouteConfigService.queryCapital();
        return RestResult.success(result);
    }
    /**
     * 路由详情
     */
    @GetMapping("/getFlowRouteConfigInfo")
    public RestResult<FlowRouteConfigInfoResponse> getFlowRouteConfigInfo(String flowId) {
        FlowRouteConfigInfoResponse result = flowRouteConfigService.getFlowRouteConfigInfo(flowId);
        return RestResult.success(result);
    }

    /**
     * 获取所有流量方
     */
    @GetMapping("/getFlowConfig")
    public RestResult<List<BankChannelResponse>> getFlowConfig() {
        List<BankChannelResponse> list = flowRouteConfigService.getFlowConfig();
        return RestResult.success(list);
    }


    /**
     * 获取所有流量方
     */
    @GetMapping("/getCouponFlowConfig")
    public RestResult<List<BankChannelResponse>> getCouponFlowConfig() {
        List<BankChannelResponse> list = flowRouteConfigService.getCouponFlowConfig();
        return RestResult.success(list);
    }

    /**
     * 流量方下挂载资金方
     */
    @PostMapping("/updateFlowRouteConfig")
    public RestResult<Void> updateFlowRouteConfig(@Validated @RequestBody FlowRouteConfigRequest reqVO) {
        flowRouteConfigService.updateFlowRouteConfig(reqVO);
        return RestResult.success();
    }

    /**
     * 资金路由分页查询
     *
     */
    @PostMapping("/queryFlowRouteConfigPage")
    public RestResult<PageInfo<FlowRouteConfigPageResponse>> queryFlowRouteConfigPage(@Validated @RequestBody PageParam reqVO){
        PageInfo<FlowRouteConfigPageResponse> result = flowRouteConfigService.queryFlowRouteConfigPage(reqVO);
        return RestResult.success(result);
    }
    /**
     * 渠道（流量方启用/禁用）
     */
    @PostMapping("/enableFlowConfig")
    public RestResult<Void> enableFlowConfig(@Validated @RequestBody EnableFlowRouteConfigReq reqVO){
        flowRouteConfigService.enableFlowConfig(reqVO);
        return RestResult.success();
    }

    /**
     * 查询流量绑卡渠道
     */
    @PostMapping("/getFlowProtocolChannelList")
    public RestResult<List<FlowProtocolChannelResponse>> getFlowProtocolChannelList(@Validated @RequestBody FlowChannelReq req) {
        List<FlowProtocolChannelResponse> list = flowRouteConfigService.getFlowProtocolChannelList(req);
        return RestResult.success(list);
    }


    /**
     * 查询流量绑卡渠道
     */
    @PostMapping("/getFlowProtocolChannelDetail")
    public RestResult<FlowProtocolChannelResponse> getFlowProtocolChannelDetail(@Validated @RequestBody FlowChannelReq req) {
        FlowProtocolChannelResponse response = flowRouteConfigService.getFlowProtocolChannelDetail(req);
        return RestResult.success(response);
    }

    /**
     * 保存流量绑卡渠道
     */
    @PostMapping("/saveFlowProtocolChannelDetail")
    public RestResult<Void> saveFlowProtocolChannelDetail(@Validated @RequestBody FlowProtocolChannelDetailReq req) {
        flowRouteConfigService.saveFlowProtocolChannelDetail(req);
        return RestResult.success();
    }

}
