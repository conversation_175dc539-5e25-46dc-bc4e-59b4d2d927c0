package com.jinghang.cash.modules.manage.vo.res;

import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.enums.RightsSupplier;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.math.BigDecimal;

public class RightsBasePackageInfoResponse {
    private Long index;
    private String id;
    /**
     * 权益供应商
     */
    private RightsSupplier channel;

    private String code;

    private String packageName;

    private String innerCode;

    private String outerCode;

    private BigDecimal sellingPrice;

    private BigDecimal costingPrice;

    private BigDecimal maxEconomizeAmount;

    /**
     * 沃橙分账金额
     */
    private BigDecimal wochengShareAmount;

    @Enumerated(EnumType.STRING)
    private AbleStatus useStatus;

    public Long getIndex() {
        return index;
    }

    public void setIndex(Long index) {
        this.index = index;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public RightsSupplier getChannel() {
        return channel;
    }

    public void setChannel(RightsSupplier channel) {
        this.channel = channel;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getInnerCode() {
        return innerCode;
    }

    public void setInnerCode(String innerCode) {
        this.innerCode = innerCode;
    }

    public String getOuterCode() {
        return outerCode;
    }

    public void setOuterCode(String outerCode) {
        this.outerCode = outerCode;
    }

    public BigDecimal getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(BigDecimal sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public BigDecimal getCostingPrice() {
        return costingPrice;
    }

    public void setCostingPrice(BigDecimal costingPrice) {
        this.costingPrice = costingPrice;
    }

    public BigDecimal getMaxEconomizeAmount() {
        return maxEconomizeAmount;
    }

    public void setMaxEconomizeAmount(BigDecimal maxEconomizeAmount) {
        this.maxEconomizeAmount = maxEconomizeAmount;
    }

    public AbleStatus getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(AbleStatus useStatus) {
        this.useStatus = useStatus;
    }

    public BigDecimal getWochengShareAmount() {
        return wochengShareAmount;
    }

    public void setWochengShareAmount(BigDecimal wochengShareAmount) {
        this.wochengShareAmount = wochengShareAmount;
    }
}
