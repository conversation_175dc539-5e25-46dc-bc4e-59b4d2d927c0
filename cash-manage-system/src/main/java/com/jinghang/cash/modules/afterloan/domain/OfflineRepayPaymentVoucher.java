package com.jinghang.cash.modules.afterloan.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 线下还款凭证记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-16
 */
@Data
@TableName("offline_repay_payment_voucher")
public class OfflineRepayPaymentVoucher implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 订单id
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 还款方式
     */
    @TableField("repay_purpose")
    private String repayPurpose;

    /**
     * 期数
     */
    @TableField("period")
    private String period;

    /**
     * 实还金额
     */
    @TableField("act_amount")
    private BigDecimal actAmount;

    /**
     * 溢出金额
     */
    @TableField("overflow_amount")
    private BigDecimal overflowAmount;
    /**
     * 外部还款号
     */
    @TableField("outer_repay_no")
    private String outerRepayNo;
    /**
     * 线下还款日期
     */
    @TableField("repay_date")
    private String repayDate;
    /**
     * bucketName
     */
    @TableField("bucket_name")
    private String bucketName;
    /**
     * ossKey
     */
    @TableField("oss_key")
    private String ossKey;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 乐观锁
     */
    @TableField("revision")
    private String revision;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;


}
