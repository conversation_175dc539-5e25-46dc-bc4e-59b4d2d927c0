package com.jinghang.cash.modules.manage.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.jinghang.cash.convert.AfterLoanConvert;
import com.jinghang.cash.enums.*;
import com.jinghang.cash.mapper.AggregatePaymentRecordMapper;
import com.jinghang.cash.mapper.CustomRepayRecordMapper;
import com.jinghang.cash.mapper.LoanMapper;
import com.jinghang.cash.mapper.OfflineRepayApplyMapper;
import com.jinghang.cash.mapper.OfflineRepayReduceMapper;
import com.jinghang.cash.mapper.OrderMapper;
import com.jinghang.cash.mapper.OrderRemarkMapper;
import com.jinghang.cash.mapper.RepayExtraGuaranteeRecordMapper;
import com.jinghang.cash.mapper.RepayPlanMapper;
import com.jinghang.cash.mapper.UserInfoMapper;
import com.jinghang.cash.modules.afterloan.domain.OfflineRepayPaymentVoucher;
import com.jinghang.cash.modules.afterloan.service.LoanSettlementService;
import com.jinghang.cash.modules.afterloan.service.OfflineRepayPaymentVoucherService;
import com.jinghang.cash.modules.manage.BizException;
import com.jinghang.cash.modules.manage.remote.BusinessRepayService;
import com.jinghang.cash.modules.manage.util.AmountUtil;
import com.jinghang.cash.modules.manage.vo.QueryAggregatePaymentVo;
import com.jinghang.cash.modules.manage.vo.req.AggregatePayApplyReq;
import com.jinghang.cash.modules.manage.vo.req.QueryAggregatePaymentReq;
import com.jinghang.cash.modules.manage.vo.req.QueryRepayPlanReq;
import com.jinghang.cash.modules.manage.vo.req.ReduceApplyReq;
import com.jinghang.cash.modules.manage.vo.req.RepayApplyPreviewReq;
import com.jinghang.cash.modules.manage.vo.req.RepayApplyReq;
import com.jinghang.cash.modules.manage.vo.req.SendPayUrlSmsReq;
import com.jinghang.cash.modules.manage.vo.req.TrialReq;
import com.jinghang.cash.modules.manage.vo.rsp.AggregatePaymentRecordResp;
import com.jinghang.cash.modules.manage.vo.rsp.QueryRepayPlanResp;
import com.jinghang.cash.modules.manage.vo.rsp.RepayApplyPreviewRsp;
import com.jinghang.cash.modules.manage.vo.rsp.RepayPlanRsp;
import com.jinghang.cash.modules.manage.vo.rsp.TrialRes;
import com.jinghang.cash.pojo.CustomRepayRecord;
import com.jinghang.cash.pojo.Loan;
import com.jinghang.cash.pojo.OfflineRepayReduce;
import com.jinghang.cash.pojo.Order;
import com.jinghang.cash.pojo.PaymentFlow;
import com.jinghang.cash.pojo.RepayExtraGuaranteeRecord;
import com.jinghang.cash.pojo.RepayPlan;
import com.jinghang.cash.utils.FileUtil;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.ppd.api.dto.RepayTrailDto;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.TrailResultDto;
import com.jinghang.ppd.api.dto.repay.ReduceApplyDto;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class AfterLoanService {
    private static final Logger logger = LoggerFactory.getLogger(AfterLoanService.class);

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private LoanMapper loanMapper;

    @Autowired
    private RepayPlanMapper repayPlanMapper;

    @Autowired
    private BusinessRepayService businessRepayService;

    @Autowired
    private OfflineRepayReduceMapper offlineRepayReduceMapper;

    @Autowired
    private OfflineRepayApplyMapper offlineRepayApplyMapper;

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private AggregatePaymentRecordMapper aggregatePaymentRecordMapper;
    @Autowired
    private OrderRemarkMapper orderRemarkMapper;

    @Autowired
    private PaymentFlowService paymentFlowService;
    @Autowired
    private CustomRepayRecordMapper customRepayRecordMapper;
    @Autowired
    private RepayExtraGuaranteeRecordMapper repayExtraGuaranteeRecordMapper;
    @Autowired
    private FileService fileService;
    @Autowired
    private OfflineRepayPaymentVoucherService offlineRepayPaymentVoucherService;

    @Value("${oss.bucket.name}")
    private String bucketName;

    private static final String ALREADY_WRITTEN_OFF = "Y";

    private static final String NOT_YET_WRITTEN_OFF = "N";


    /**
     * 查询还款计划
     *
     * @param req
     */
    public QueryRepayPlanResp queryRepayPlan(QueryRepayPlanReq req) {
        List<Order> orders = getOrders(req);

        if (orders.isEmpty()) {
            throw new RuntimeException("未查询到订单");
        } else if (orders.size() > 1) {
            throw new RuntimeException("查询到多笔订单");
        }

        Order order = orders.get(0);

        Loan loan = loanMapper.selectByOrderId(order.getId());

        //查询还款计划
        List<RepayPlan> repayPlans = repayPlanMapper.selectList(new LambdaQueryWrapper<RepayPlan>()
            .eq(RepayPlan::getLoanId, loan.getId())
            .orderByAsc(RepayPlan::getPeriod));

        List<RepayPlanRsp> repayPlanList = new ArrayList<>();
        for (RepayPlan repayPlan : repayPlans) {
            RepayPlanRsp repayPlanRsp = getRepayPlanRsp(repayPlan);
            repayPlanList.add(repayPlanRsp);
        }

        QueryRepayPlanResp res = new QueryRepayPlanResp();
        res.setOrderId(order.getId());
        Optional.ofNullable(userInfoMapper.selectById(order.getUserId())).ifPresent(userInfo -> {
            res.setCertNo(userInfo.getCertNo());
            res.setUserName(userInfo.getName());
            res.setMobile(userInfo.getMobile());
        });
        res.setRepayPlanList(repayPlanList);
        return res;
    }

    private RepayPlanRsp getRepayPlanRsp(RepayPlan repayPlan) {
        Date date = DateUtil.atDayStart(new Date());
        RepayPlanRsp repayPlanRsp = AfterLoanConvert.INSTANCE.toRepayPlanRsp(repayPlan);
        if (RepayConstant.NORMAL.name().equals(repayPlanRsp.getCustRepayState())) {
            RepayExtraGuaranteeRecord repayExtraGuaranteeRecord = repayExtraGuaranteeRecordMapper
                    .findTopByLoanIdAndPeriodAndRepayState(repayPlan.getLoanId(), repayPlan.getPeriod(), ProcessState.FAILED.name());
            if (Objects.nonNull(repayExtraGuaranteeRecord)) {
                repayPlanRsp.setFailMsg(repayExtraGuaranteeRecord.getUpdatedTime().format(DateUtil.NEW_FORMATTER)
                        + "：" + repayExtraGuaranteeRecord.getFailReason());
            } else {
                CustomRepayRecord latestFailRepayRecord = customRepayRecordMapper.findLatestFailRepayRecord(repayPlan.getLoanId(), repayPlan.getPeriod());
                if (Objects.nonNull(latestFailRepayRecord)) {
                    repayPlanRsp.setFailMsg(new SimpleDateFormat(DateUtil.NEW_PATTERN).format(latestFailRepayRecord.getUpdatedTime())
                            + "：" + latestFailRepayRecord.getFailReason());
                }
            }
            //未还款
            if (repayPlanRsp.getPlanRepayDate().after(date)) {
                repayPlanRsp.setNowRepayState("未到期未还");
            } else if (repayPlanRsp.getPlanRepayDate().equals(date)) {
                repayPlanRsp.setNowRepayState("当期未还");
            } else {
                repayPlanRsp.setNowRepayState("逾期");
            }
            repayPlanRsp.setCustRepayState("未还款");
        } else {
            //已还款
            if (repayPlanRsp.getPlanRepayDate().after(date)) {
                repayPlanRsp.setNowRepayState("已还");
            } else {
                repayPlanRsp.setNowRepayState("已还");
            }
            repayPlanRsp.setCustRepayState("已还款");
        }
        return repayPlanRsp;
    }


    private List<Order> getOrders(QueryRepayPlanReq req) {
        List<Order> orders = orderMapper.selectList(new LambdaQueryWrapper<Order>()
                .eq(StringUtils.isNotBlank(req.getOrderId()), Order::getId, req.getOrderId())
                .eq(StringUtils.isBlank(req.getOrderId()) && StringUtils.isNotBlank(req.getMobile()), Order::getMobile, req.getMobile())
                .eq(StringUtils.isBlank(req.getOrderId()) && StringUtils.isNotBlank(req.getCertNo()), Order::getCertNo, req.getCertNo())
                .eq(StringUtils.isBlank(req.getOrderId()), Order::getOrderState, OrderState.LOAN_PASS));
        return orders;
    }

    /**
     * 试算
     *
     * @param req
     * @return
     */
    public TrialRes trial(TrialReq req) {

        //查询loan
        Loan loan = loanMapper.selectByOrderId(req.getOrderId());

        if (RepayPurpose.CURRENT == req.getRepayPurpose()) {
            //试算当期

            //查询最早一期逾期的订单
            RepayPlan repayPlan = repayPlanMapper.queryEarliestOverdue(loan.getId());
            if (Objects.isNull(repayPlan)) {
                throw new RuntimeException("不存在逾期的还款计划");
            }

            req.setPeriod(repayPlan.getPeriod());
        }

        RepayTrailDto dto = new RepayTrailDto();
        dto.setLoanId(loan.getId());
        dto.setPeriod(req.getPeriod());
        dto.setRepayPurpose(AfterLoanConvert.INSTANCE.toRepayPurpose(req.getRepayPurpose()));
        RestResult<com.jinghang.ppd.api.dto.TrailResultDto> restResult = businessRepayService.trial(dto);
        if (!restResult.isSuccess()) {
            throw new RuntimeException("试算异常:" + restResult.getMsg());
        }

        TrialRes trialRes = AfterLoanConvert.INSTANCE.toTrialRes(restResult.getData());
        trialRes.setPeriod(req.getPeriod());
        return trialRes;
    }

    /**
     * 减免申请
     *
     * @param req
     */
    public void reduceApply(ReduceApplyReq req) {
        ReduceApplyDto reduceApplyDto = AfterLoanConvert.INSTANCE.toReduceApplyDto(req);
        //reduceApplyDto.setOperator(SecurityUtils.getCurrentUsername());
        RestResult<Void> result = businessRepayService.reduceApply(reduceApplyDto);
        if (!result.isSuccess()){
            throw new RuntimeException(result.getMsg());
        }
    }


    /**
     * 销账
     *
     * @param req
     */
    public void repayApply(RepayApplyReq req, MultipartFile file) {
        // 生成ossKey
        String ossKey = String.format("offlinerepay/image/%s/payment_voucher_%s.%s",
                DateUtil.formatShort(new Date()),
                UUID.randomUUID().toString().replaceAll("-", ""),
                file.getOriginalFilename().split("\\.")[1]);
        try {
            fileService.uploadOss(bucketName, ossKey, file.getInputStream());
        } catch (IOException e) {
            logger.error("线下打款凭证上传失败", e);
            throw new RuntimeException(e);
        }

        RepayApplyDto repayApplyDto = AfterLoanConvert.INSTANCE.toRepayApplyDto(req);
        // 外部还款流水号
        String outerRepayNo = UUID.randomUUID().toString().replaceAll("-", "");
        repayApplyDto.setOuterRepayNo(outerRepayNo);
        repayApplyDto.setOverflowAmount(AmountUtil.safeAmount(req.getOverflowAmount()));
        RestResult<Void> result = businessRepayService.repayApply(repayApplyDto);
        if (!result.isSuccess()){
            throw new RuntimeException(result.getMsg());
        }

        OfflineRepayPaymentVoucher voucher = new OfflineRepayPaymentVoucher();
        voucher.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        voucher.setOrderId(req.getOrderId());
        voucher.setRepayPurpose(req.getRepayPurpose().name());
        voucher.setPeriod(req.getPeriod() != null ? req.getPeriod().toString() : null);
        voucher.setActAmount(req.getActAmount());
        voucher.setOverflowAmount( req.getOverflowAmount());
        voucher.setOuterRepayNo(outerRepayNo);
        voucher.setRepayDate(req.getRepayDate());
        voucher.setBucketName(bucketName);
        voucher.setOssKey(ossKey);
        voucher.setCreatedTime(LocalDateTime.now());
        offlineRepayPaymentVoucherService.save(voucher);
    }


    /**
     * 还款销账预览/聚合支付预览
     *
     * @param req
     */
    public RepayApplyPreviewRsp repayApplyPreview(RepayApplyPreviewReq req) {
        //查询loan
        Loan loan = loanMapper.selectByOrderId(req.getOrderId());

        OfflineRepayReduce offlineRepayReduce = offlineRepayReduceMapper.selectOne(new LambdaQueryWrapper<OfflineRepayReduce>()
            .eq(OfflineRepayReduce::getOrderId, req.getOrderId())
            .eq(OfflineRepayReduce::getRepayPurpose, req.getRepayPurpose())
            .eq(OfflineRepayReduce::getAuditState, AuditStatus.PASS)
            .eq(OfflineRepayReduce::getUseState, UseState.WAIT)
            .orderByAsc(OfflineRepayReduce::getPeriod)
            .last("limit 1")
        );

        RepayApplyPreviewRsp rsp = new RepayApplyPreviewRsp();
        if (offlineRepayReduce != null) {
            rsp.setAmount(offlineRepayReduce.getAmount());
            rsp.setPeriod(offlineRepayReduce.getPeriod());
            rsp.setActAmount(offlineRepayReduce.getActAmount());
            rsp.setReduceAmount(offlineRepayReduce.getReduceAmount());
        } else {
            //试算
            RepayTrailDto dto = new RepayTrailDto();
            dto.setLoanId(loan.getId());
            if (RepayPurpose.CURRENT == req.getRepayPurpose()) {
                //试算当期
                //查询最早一期逾期的订单
                RepayPlan repayPlan = repayPlanMapper.queryEarliestOverdue(loan.getId());
                if (Objects.isNull(repayPlan)) {
                    throw new RuntimeException("不存在逾期的还款计划");
                }
                dto.setPeriod(repayPlan.getPeriod());
                rsp.setPeriod(repayPlan.getPeriod());
            }
            dto.setRepayPurpose(AfterLoanConvert.INSTANCE.toRepayPurpose(req.getRepayPurpose()));
            RestResult<TrailResultDto> restResult = businessRepayService.trial(dto);
            if (!restResult.isSuccess()) {
                throw new RuntimeException("试算异常:" + restResult.getMsg());
            }
            rsp.setAmount(restResult.getData().getAmount());
            rsp.setActAmount(restResult.getData().getAmount());
            rsp.setReduceAmount(BigDecimal.ZERO);
        }

        return rsp;
    }







    /**
     * 下载聚合支付记录excel文档
     * @param req
     * @param httpResp
     * @throws IOException
     */
    public void downloadExcel(QueryAggregatePaymentReq req, HttpServletResponse httpResp) throws IOException {
        logger.info("聚合支付数据导出入参:{}", JsonUtil.toJsonString(req));
        List<QueryAggregatePaymentVo> vos = assembleRes(queryRecord(req, false), true);
        List<Map<String, Object>> list = new ArrayList<>();
        for (QueryAggregatePaymentVo vo : vos) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("订单编号", vo.getOrderId());
            map.put("姓名", vo.getName());
            map.put("手机号", vo.getMobile());
            map.put("订单状态", ApplyStateEnum.getMsg(vo.getApplyState()));
            map.put("聚合订单号", vo.getId());
            map.put("销账状态", ALREADY_WRITTEN_OFF.equals(getWriteOffStatus(vo.getOrderId(), vo.getPeriod())) ? "已销账" : "未销账");
            map.put("发起时间", vo.getApplyTime());
            map.put("支付时间", vo.getPayTime());
            map.put("支付结果", PayResultEnum.getMsg(vo.getPayResult()));
            map.put("还款期数", vo.getPeriod());
            map.put("应还金额", vo.getAmount());
            map.put("减免金额", vo.getReduceAmount());
            map.put("减免后金额", vo.getActAmount());
            map.put("支付通道", vo.getPayChannel());
            map.put("支付链接", vo.getPayUrl());
            list.add(map);
        }
        //导出聚合支付记录数据
        FileUtil.downloadExcel(list, httpResp);
    }

    private Page<QueryAggregatePaymentVo> assembleRes(Page<AggregatePaymentRecordResp> records, boolean isQHYP) {
        Page<QueryAggregatePaymentVo> vos = new Page<>();
        for (AggregatePaymentRecordResp resp : records) {
            QueryAggregatePaymentVo vo = new QueryAggregatePaymentVo();
            vo.setId(resp.getId());
            vo.setOrderId(resp.getOrderId());
            vo.setName(resp.getName());
            vo.setMobile(resp.getMobile());
            vo.setApplyState(resp.getApplyState());
            vo.setApplyTime(resp.getApplyTime());
            vo.setPayTime(resp.getPayTime());
            vo.setPayResult(resp.getPayResult());
            vo.setPeriod(resp.getPeriod());
            vo.setAmount(resp.getAmount());
            vo.setReduceAmount(resp.getReduceAmount());
            vo.setActAmount(resp.getActAmount());
            vo.setPayChannel(resp.getPayChannel());
            vo.setPayUrl(resp.getPayUrl());
            vo.setProductChannel(resp.getProductChannel());
            vo.setRemark(resp.getRemark());
            PaymentFlow paymentFlowByAgreement = paymentFlowService.getPaymentFlowByAgreementPayId(resp.getId());
            if (null != paymentFlowByAgreement) {
                vo.setOuterOrderNo(paymentFlowByAgreement.getOuterOrderNo());
                vo.setMerchantOutOrderNo(paymentFlowByAgreement.getMerchantOutOrderNo());
                vo.setPayerAcctCode(paymentFlowByAgreement.getPayerAcctCode());
                vo.setPayerUserName(paymentFlowByAgreement.getPayerUserName());
                vo.setOuterTradeNo(paymentFlowByAgreement.getOuterTradeNo());
            }
            // 只有贷后管理页面的轻花优品才需要查询销账状态
            if (isQHYP) {
                vo.setWriteOffStatus(getWriteOffStatus(resp.getOrderId(), resp.getPeriod()));
            }
            vos.add(vo);
        }
        vos.setEndRow(records.getEndRow());
        vos.setPages(records.getPages());
        vos.setStartRow(records.getStartRow());
        vos.setPageNum(records.getPageNum());
        vos.setPages(records.getPages());
        vos.setTotal(records.getTotal());
        return vos;
    }

    /**
     * 分页查询聚合支付记录
     * @param req
     * @param isQuery true-查询功能，false-导出功能
     * @return
     */
    private Page<AggregatePaymentRecordResp> queryRecord(QueryAggregatePaymentReq req, boolean isQuery) {
        if (isQuery) {
            PageHelper.startPage(req.getPageNum(), req.getPageSize());
        }
        if (StringUtils.isBlank(req.getPayerAcctCode())) {
            return aggregatePaymentRecordMapper.queryList(req);
        } else {
            return aggregatePaymentRecordMapper.queryListByPayerAcctCode(req);
        }
    }

    /**
     * 获得销账状态
     * @param orderId
     * @param period
     * @return
     */
    private String getWriteOffStatus(String orderId, Integer period) {
        boolean isWrittenOff = offlineRepayApplyMapper.existsByOrderIdAndPeriodAndApplyState(orderId, period, ProcessState.SUCCEED);
        if (isWrittenOff) {
            return  ALREADY_WRITTEN_OFF;
        }
        return NOT_YET_WRITTEN_OFF;
    }
}
