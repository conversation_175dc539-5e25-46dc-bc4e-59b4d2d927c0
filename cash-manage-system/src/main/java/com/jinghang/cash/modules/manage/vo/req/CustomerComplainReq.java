package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.modules.manage.vo.PageParam;
import lombok.Data;

/**
 * 客户投诉查询
 */
@Data
public class CustomerComplainReq extends PageParam {

    private String mobile;
    private String certNo;
    private String orderNo;
    /**
     * 放款渠道 资金方
     */
    private String bankChannel;
    /**
     * 投诉渠道
     */
    private String complainChannel;
    /**
     * 投诉类型
     */
    private String complainType;
    /**
     * 是否逾期
     */
    private String overdueState;
    /**
     * 处理状态
     */
    private String handleState;
    /**
     * 订单状态
     */
    private String orderState;
    private String startTime;
    private String endTime;

    /**
     * 完成时间
     */
    private String completeStartTime;
    private String completeEndTime;
}
