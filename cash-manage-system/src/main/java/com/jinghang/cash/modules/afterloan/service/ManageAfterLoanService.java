package com.jinghang.cash.modules.afterloan.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.cash.convert.AfterLoanConvert;
import com.jinghang.cash.enums.*;
import com.jinghang.cash.mapper.*;
import com.jinghang.cash.modules.afterloan.service.LoanSettlementService;
import com.jinghang.cash.modules.manage.BizException;
import com.jinghang.cash.modules.manage.service.AfterLoanService;
import com.jinghang.cash.modules.manage.util.AmountUtil;
import com.jinghang.cash.modules.manage.vo.req.QueryRepayPlanReq;
import com.jinghang.cash.modules.manage.vo.req.TrialReq;
import com.jinghang.cash.modules.manage.vo.rsp.QueryRepayPlanResp;
import com.jinghang.cash.modules.manage.vo.rsp.RepayPlanRsp;
import com.jinghang.cash.modules.manage.vo.rsp.TrialRes;
import com.jinghang.cash.pojo.*;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:
 * @desc:
 * @date 2025/9/17 17:03
 */
@Service
public class ManageAfterLoanService {

    private static final Logger logger = LoggerFactory.getLogger(AfterLoanService.class);

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private LoanMapper loanMapper;
    @Autowired
    private RepayPlanMapper repayPlanMapper;
    @Autowired
    private UserInfoMapper userInfoMapper;
    @Autowired
    private RepayExtraGuaranteeRecordMapper repayExtraGuaranteeRecordMapper;
    @Autowired
    private CustomRepayRecordMapper customRepayRecordMapper;
    @Autowired
    private LoanSettlementService loanSettlementService;

    /**
     * 单期逾期登记
     * @param req
     * @return
     */
    public QueryRepayPlanResp querySingleRepayPlan(QueryRepayPlanReq req) {
        List<Order> orders = getOrders(req);

        if (orders.isEmpty()) {
            throw new BizException(ResultCode.QUERY_NO_ORDER);
        } else if (orders.size() > 1) {
            throw new BizException(ResultCode.QUERY_MULTI_ORDER);
        }

        Order order = orders.get(0);
        Loan loan = loanMapper.selectByOrderId(order.getId());

        if (loan == null) {
            throw new BizException(ResultCode.LOAN_NOT_EXIST);
        }

        //查询还款计划
        List<RepayPlan> repayPlans = repayPlanMapper.selectList(new LambdaQueryWrapper<RepayPlan>()
                .eq(RepayPlan::getLoanId, loan.getId())
                .eq(RepayPlan::getCustRepayState, RepayConstant.NORMAL.name())
                .orderByAsc(RepayPlan::getPeriod));

        if (CollectionUtil.isEmpty(repayPlans)) {
            throw new BizException(ResultCode.QUERY_NO_REPAY_PLAN);
        }
        Date date = DateUtil.atDayStart(new Date());
        List<RepayPlan> repayPlanList = new ArrayList<>();
        for (RepayPlan repayPlan : repayPlans) {
            if (repayPlan.getPlanRepayDate().after(date) || repayPlan.getPlanRepayDate().equals(date)) {
                continue;
            }
            repayPlanList.add(repayPlan);
        }
        if (CollectionUtil.isEmpty(repayPlanList)) {
            throw new BizException(ResultCode.QUERY_NOT_OVER_DAY_REPAY_PLAN);
        } else if (repayPlanList.size() > 1) {
            throw new BizException(ResultCode.QUERY_MULTI_OVER_DAY_REPAY_PLAN);
        }

        RepayPlan repayPlan = repayPlanList.get(0);
        RepayPlanRsp repayPlanRsp = getRepayPlanRsp(repayPlan);
        TrialReq trialReq = new TrialReq();
        trialReq.setOrderId(req.getOrderId());
        trialReq.setPeriod(repayPlanRsp.getPeriod());
        trialReq.setRepayPurpose(RepayPurpose.CURRENT);
        TrialRes trialRes = loanSettlementService.trial(trialReq);
        repayPlanRsp.setPenaltyAmt(AmountUtil.safeAmount(trialRes.getPenalty()));
        repayPlanRsp.setActBreachAmt(AmountUtil.safeAmount(trialRes.getBreachFee()));
        repayPlanRsp.setAmount(AmountUtil.sum(
                repayPlanRsp.getPrincipalAmt(),
                repayPlanRsp.getInterestAmt(),
                repayPlanRsp.getPenaltyAmt(),
                repayPlanRsp.getConsultFee(),
                repayPlanRsp.getGuaranteeAmt(),
                repayPlanRsp.getActBreachAmt()));

        QueryRepayPlanResp res = new QueryRepayPlanResp();
        res.setOrderId(order.getId());
        Optional.ofNullable(userInfoMapper.selectById(order.getUserId())).ifPresent(userInfo -> {
            res.setCertNo(userInfo.getCertNo());
            res.setUserName(userInfo.getName());
            res.setMobile(userInfo.getMobile());
        });
        res.setRepayPlanList(Arrays.asList(repayPlanRsp));
        return res;
    }

    /**
     * 多期逾期登记
     * @param req
     * @return
     */
    public QueryRepayPlanResp queryMultiRepayPlan(QueryRepayPlanReq req) {
        List<Order> orders = getOrders(req);

        if (orders.isEmpty()) {
            throw new BizException(ResultCode.QUERY_NO_ORDER);
        } else if (orders.size() > 1) {
            throw new BizException(ResultCode.QUERY_MULTI_ORDER);
        }

        Order order = orders.get(0);
        Loan loan = loanMapper.selectByOrderId(order.getId());

        if (loan == null) {
            throw new BizException(ResultCode.LOAN_NOT_EXIST);
        }

        //查询还款计划
        List<RepayPlan> repayPlans = repayPlanMapper.selectList(new LambdaQueryWrapper<RepayPlan>()
                .eq(RepayPlan::getLoanId, loan.getId())
                .eq(RepayPlan::getCustRepayState, RepayConstant.NORMAL.name())
                .orderByAsc(RepayPlan::getPeriod));

        if (CollectionUtil.isEmpty(repayPlans)) {
            throw new BizException(ResultCode.QUERY_NO_REPAY_PLAN);
        }

        QueryRepayPlanResp res = new QueryRepayPlanResp();
        List<RepayPlanRsp> repayPlanList = new ArrayList<>();
        Date date = DateUtil.atDayStart(new Date());
        for (RepayPlan repayPlan : repayPlans) {
            if (repayPlan.getPlanRepayDate().after(date) || repayPlan.getPlanRepayDate().equals(date)) {
                continue;
            }
            RepayPlanRsp repayPlanRsp = getRepayPlanRsp(repayPlan);
            // res.setTotalAmount(AmountUtil.sum(res.getTotalAmount(), repayPlanRsp.getAmount()));
            res.setTotalConsultFee(AmountUtil.sum(res.getTotalConsultFee(), repayPlanRsp.getConsultFee()));
            res.setTotalGuaranteeAmt(AmountUtil.sum(res.getTotalGuaranteeAmt(), repayPlanRsp.getGuaranteeAmt()));
            res.setTotalInterestAmt(AmountUtil.sum(res.getTotalInterestAmt(), repayPlanRsp.getInterestAmt()));
            // res.setTotalPenaltyAmt(AmountUtil.sum(res.getTotalPenaltyAmt(), repayPlanRsp.getPenaltyAmt()));
            res.setTotalPrincipalAmt(AmountUtil.sum(res.getTotalPrincipalAmt(), repayPlanRsp.getPrincipalAmt()));
            //逾期没有罚息
            // res.setTotalBreachAmt(AmountUtil.sum(res.getTotalBreachAmt(), repayPlanRsp.getActBreachAmt()));
            if (res.getOverdueStartPeriod() == null || res.getOverdueEndPeriod() == null) {
                res.setOverdueStartPeriod(repayPlanRsp.getPeriod());
                res.setOverdueEndPeriod(repayPlanRsp.getPeriod());
            } else {
                if (repayPlanRsp.getPeriod() > res.getOverdueEndPeriod()) {
                    res.setOverdueEndPeriod(repayPlanRsp.getPeriod());
                }
            }
            repayPlanList.add(repayPlanRsp);
        }

        if (CollectionUtil.isEmpty(repayPlanList)) {
            throw new BizException(ResultCode.QUERY_NOT_OVER_DAY_REPAY_PLAN);
        }

        // 试算
        TrialReq trialReq = new TrialReq();
        trialReq.setOrderId(req.getOrderId());
        trialReq.setPeriod(repayPlanList.get(0).getPeriod());
        trialReq.setRepayPurpose(RepayPurpose.CURRENT);
        TrialRes trialRes = loanSettlementService.trial(trialReq);
        res.setTotalPenaltyAmt(AmountUtil.safeAmount(trialRes.getPenalty()));
        res.setTotalBreachAmt(AmountUtil.safeAmount(trialRes.getBreachFee()));
        res.setTotalAmount(AmountUtil.sum(
                res.getTotalPrincipalAmt(),
                res.getTotalInterestAmt(),
                res.getTotalPenaltyAmt(),
                res.getTotalConsultFee(),
                res.getTotalGuaranteeAmt(),
                res.getTotalBreachAmt()));

        res.setOverdueTotalPeriod(res.getOverdueEndPeriod()-res.getOverdueStartPeriod()+1);

        res.setOrderId(order.getId());
        Optional.ofNullable(userInfoMapper.selectById(order.getUserId())).ifPresent(userInfo -> {
            res.setCertNo(userInfo.getCertNo());
            res.setUserName(userInfo.getName());
            res.setMobile(userInfo.getMobile());
        });
        res.setRepayPlanList(repayPlanList);
        return res;
    }


    /**
     * 结清登记
     * @param req
     * @return
     */
    public QueryRepayPlanResp queryClearRepayPlan(QueryRepayPlanReq req) {
        List<Order> orders = getOrders(req);

        if (orders.isEmpty()) {
            throw new BizException(ResultCode.QUERY_NO_ORDER);
        } else if (orders.size() > 1) {
            throw new BizException(ResultCode.QUERY_MULTI_ORDER);
        }

        Order order = orders.get(0);
        Loan loan = loanMapper.selectByOrderId(order.getId());

        if (loan == null) {
            throw new BizException(ResultCode.LOAN_NOT_EXIST);
        }

        //查询还款计划
        List<RepayPlan> repayPlans = repayPlanMapper.selectList(new LambdaQueryWrapper<RepayPlan>()
                .eq(RepayPlan::getLoanId, loan.getId())
                .eq(RepayPlan::getCustRepayState, RepayConstant.NORMAL.name())
                .orderByAsc(RepayPlan::getPeriod));

        if (CollectionUtil.isEmpty(repayPlans)) {
            throw new BizException(ResultCode.QUERY_NO_REPAY_PLAN);
        }

        QueryRepayPlanResp res = new QueryRepayPlanResp();
        List<RepayPlanRsp> repayPlanList = new ArrayList<>();
        for (RepayPlan repayPlan : repayPlans) {
            RepayPlanRsp repayPlanRsp = getRepayPlanRsp(repayPlan);
            //res.setTotalAmount(AmountUtil.sum(res.getTotalAmount(), repayPlanRsp.getAmount()));
            res.setTotalConsultFee(AmountUtil.sum(res.getTotalConsultFee(), repayPlanRsp.getConsultFee()));
            res.setTotalGuaranteeAmt(AmountUtil.sum(res.getTotalGuaranteeAmt(), repayPlanRsp.getGuaranteeAmt()));
            res.setTotalInterestAmt(AmountUtil.sum(res.getTotalInterestAmt(), repayPlanRsp.getInterestAmt()));
            //res.setTotalPenaltyAmt(AmountUtil.sum(res.getTotalPenaltyAmt(), repayPlanRsp.getPenaltyAmt()));
            res.setTotalPrincipalAmt(AmountUtil.sum(res.getTotalPrincipalAmt(), repayPlanRsp.getPrincipalAmt()));
            //res.setTotalBreachAmt(AmountUtil.sum(res.getTotalBreachAmt(), repayPlanRsp.getActBreachAmt()));
            repayPlanList.add(repayPlanRsp);
        }

        // 结清试算
        TrialReq trialReq = new TrialReq();
        trialReq.setOrderId(req.getOrderId());
        trialReq.setPeriod(repayPlanList.get(0).getPeriod());
        trialReq.setRepayPurpose(RepayPurpose.CLEAR);
        TrialRes trialRes = loanSettlementService.trial(trialReq);
        res.setTotalPenaltyAmt(AmountUtil.safeAmount(trialRes.getPenalty()));
        res.setTotalBreachAmt(AmountUtil.safeAmount(trialRes.getBreachFee()));
        res.setTotalAmount(AmountUtil.sum(
                res.getTotalPrincipalAmt(),
                res.getTotalInterestAmt(),
                res.getTotalPenaltyAmt(),
                res.getTotalConsultFee(),
                res.getTotalGuaranteeAmt(),
                res.getTotalBreachAmt()));

        res.setOrderId(order.getId());
        Optional.ofNullable(userInfoMapper.selectById(order.getUserId())).ifPresent(userInfo -> {
            res.setCertNo(userInfo.getCertNo());
            res.setUserName(userInfo.getName());
            res.setMobile(userInfo.getMobile());
        });
        res.setRepayPlanList(repayPlanList);
        return res;
    }



    private RepayPlanRsp getRepayPlanRsp(RepayPlan repayPlan) {
        Date date = DateUtil.atDayStart(new Date());
        RepayPlanRsp repayPlanRsp = AfterLoanConvert.INSTANCE.toRepayPlanRsp(repayPlan);
        if (RepayConstant.NORMAL.name().equals(repayPlanRsp.getCustRepayState())) {
            RepayExtraGuaranteeRecord repayExtraGuaranteeRecord = repayExtraGuaranteeRecordMapper
                    .findTopByLoanIdAndPeriodAndRepayState(repayPlan.getLoanId(), repayPlan.getPeriod(), ProcessState.FAILED.name());
            if (Objects.nonNull(repayExtraGuaranteeRecord)) {
                repayPlanRsp.setFailMsg(repayExtraGuaranteeRecord.getUpdatedTime().format(DateUtil.NEW_FORMATTER)
                        + "：" + repayExtraGuaranteeRecord.getFailReason());
            } else {
                CustomRepayRecord latestFailRepayRecord = customRepayRecordMapper.findLatestFailRepayRecord(repayPlan.getLoanId(), repayPlan.getPeriod());
                if (Objects.nonNull(latestFailRepayRecord)) {
                    repayPlanRsp.setFailMsg(new SimpleDateFormat(DateUtil.NEW_PATTERN).format(latestFailRepayRecord.getUpdatedTime())
                            + "：" + latestFailRepayRecord.getFailReason());
                }
            }
            //未还款
            if (repayPlanRsp.getPlanRepayDate().after(date)) {
                repayPlanRsp.setNowRepayState("未到期未还");
            } else if (repayPlanRsp.getPlanRepayDate().equals(date)) {
                repayPlanRsp.setNowRepayState("当期未还");
            } else {
                repayPlanRsp.setNowRepayState("逾期");
            }
            repayPlanRsp.setCustRepayState("未还款");
        } else {
            //已还款
            if (repayPlanRsp.getPlanRepayDate().after(date)) {
                repayPlanRsp.setNowRepayState("已还");
            } else {
                repayPlanRsp.setNowRepayState("已还");
            }
            repayPlanRsp.setCustRepayState("已还款");
        }
        return repayPlanRsp;
    }


    private List<Order> getOrders(QueryRepayPlanReq req) {
        List<Order> orders = orderMapper.selectList(new LambdaQueryWrapper<Order>()
                .eq(StringUtils.isNotBlank(req.getOrderId()), Order::getId, req.getOrderId())
                .eq(StringUtils.isBlank(req.getOrderId()) && StringUtils.isNotBlank(req.getMobile()), Order::getMobile, req.getMobile())
                .eq(StringUtils.isBlank(req.getOrderId()) && StringUtils.isNotBlank(req.getCertNo()), Order::getCertNo, req.getCertNo())
                .eq(StringUtils.isBlank(req.getOrderId()), Order::getOrderState, OrderState.LOAN_PASS));
        return orders;
    }
}
