package com.jinghang.cash.modules.afterloan.controller;

import com.jinghang.cash.modules.afterloan.service.ManageAfterLoanService;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.vo.req.QueryRepayPlanReq;
import com.jinghang.cash.modules.manage.vo.rsp.QueryRepayPlanResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:
 * @desc:
 * @date 2025/9/17 17:03
 */
@RestController
@RequestMapping("/manageAfterLoan")
public class ManageAfterLoanController {

    @Autowired
    private ManageAfterLoanService manageAfterLoanService;

    /**
     * 单期逾期登记
     * @return
     */
    @PostMapping("/querySingleRepayPlan")
    public RestResult<QueryRepayPlanResp> querySingleRepayPlan(@RequestBody QueryRepayPlanReq req) {
        if (StringUtils.isAllBlank(req.getOrderId(), req.getMobile(), req.getCertNo())) {
            throw new RuntimeException("缺少查询参数");
        }
        QueryRepayPlanResp res = manageAfterLoanService.querySingleRepayPlan(req);
        return RestResult.success(res);
    }

    /**
     * 多期逾期登记
     * @return
     */
    @PostMapping("/queryMultiRepayPlan")
    public RestResult<QueryRepayPlanResp> queryMultiRepayPlan(@RequestBody QueryRepayPlanReq req) {
        if (StringUtils.isAllBlank(req.getOrderId(), req.getMobile(), req.getCertNo())) {
            throw new RuntimeException("缺少查询参数");
        }
        QueryRepayPlanResp res = manageAfterLoanService.queryMultiRepayPlan(req);
        return RestResult.success(res);
    }

    /**
     * 结清登记
     * @param req
     * @return
     */
    @PostMapping("/queryClearRepayPlan")
    public RestResult<QueryRepayPlanResp> queryClearRepayPlan(@RequestBody QueryRepayPlanReq req) {
        if (StringUtils.isAllBlank(req.getOrderId(), req.getMobile(), req.getCertNo())) {
            throw new RuntimeException("缺少查询参数");
        }
        QueryRepayPlanResp res = manageAfterLoanService.queryClearRepayPlan(req);
        return RestResult.success(res);
    }
}
