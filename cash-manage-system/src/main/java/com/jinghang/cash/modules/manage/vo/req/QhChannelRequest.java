package com.jinghang.cash.modules.manage.vo.req;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR> gale
 * @Classname QhChannelRequest
 * @Description TODO
 * @Date 2024/3/23 17:38
 */
public class QhChannelRequest {
    @NotBlank
    private String clientNum;
    @NotBlank
    private String channelName;
    @NotBlank
    private String channelNum;

    private String versionNum;

    public String getClientNum() {
        return clientNum;
    }

    public void setClientNum(String clientNum) {
        this.clientNum = clientNum;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getChannelNum() {
        return channelNum;
    }

    public void setChannelNum(String channelNum) {
        this.channelNum = channelNum;
    }

    public String getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(String versionNum) {
        this.versionNum = versionNum;
    }
}
