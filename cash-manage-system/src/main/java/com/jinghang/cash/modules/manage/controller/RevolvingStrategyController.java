package com.jinghang.cash.modules.manage.controller;

import com.github.pagehelper.PageInfo;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.service.RevolvingStrategyConfigService;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyRelateQueryRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyRelateSaveRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigDetailRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigPagingRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigSaveRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigStatusRequest;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyRelateResponse;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyConfigResponse;
import com.jinghang.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@RestController
@RequestMapping("/revolving/strategy")
@Slf4j
public class RevolvingStrategyController {

    @Autowired
    private RevolvingStrategyConfigService revolvingStrategyConfigService;

    @PostMapping("/paging")
    public RestResult<PageInfo<RevolvingStrategyConfigResponse>> paging(@RequestBody RevolvingStrategyConfigPagingRequest request) {
        log.info("分页查询循环策略, {}", JsonUtil.toJsonString(request));
        PageInfo<RevolvingStrategyConfigResponse> result = revolvingStrategyConfigService.paging(request);
        log.info("分页查询循环策略, 返回结果: {}", JsonUtil.toJsonString(result));
        return RestResult.success(result);
    }

    @PostMapping("/save")
    public RestResult<Boolean> save(@RequestBody @Valid RevolvingStrategyConfigSaveRequest request) {
        log.info("保存循环策略, {}", JsonUtil.toJsonString(request));
        revolvingStrategyConfigService.save(request);
        return RestResult.success(Boolean.TRUE);
    }

    @PostMapping("/detail")
    public RestResult<RevolvingStrategyConfigResponse> detail(@RequestBody @Valid RevolvingStrategyConfigDetailRequest request) {
        log.info("查询循环策略详情, {}", JsonUtil.toJsonString(request));
        RevolvingStrategyConfigResponse result = revolvingStrategyConfigService.detail(request);
        log.info("查询循环策略详情, 返回结果: {}", JsonUtil.toJsonString(result));
        return RestResult.success(result);
    }

    @PostMapping("/enable")
    public RestResult<Boolean> enable(@RequestBody @Valid RevolvingStrategyConfigStatusRequest request) {
        log.info("启用循环策略, {}", JsonUtil.toJsonString(request));
        revolvingStrategyConfigService.enable(request);
        return RestResult.success(Boolean.TRUE);
    }

    @PostMapping("/disable")
    public RestResult<Boolean> disable(@RequestBody @Valid RevolvingStrategyConfigStatusRequest request) {
        log.info("禁用循环策略, {}", JsonUtil.toJsonString(request));
        revolvingStrategyConfigService.disable(request);
        return RestResult.success(Boolean.TRUE);
    }

    @PostMapping("/relate/save")
    public RestResult<Boolean> relateSave(@RequestBody @Valid RevolvingStrategyRelateSaveRequest request) {
        log.info("保存循环策略分箱, {}", JsonUtil.toJsonString(request));
        revolvingStrategyConfigService.relateSave(request);
        return RestResult.success(Boolean.TRUE);
    }

    @PostMapping("/relate/query")
    public RestResult<List<RevolvingStrategyRelateResponse>> relateQuery(@RequestBody @Valid RevolvingStrategyRelateQueryRequest request) {
        log.info("查询循环策略分箱, {}", JsonUtil.toJsonString(request));
        List<RevolvingStrategyRelateResponse> responseList = revolvingStrategyConfigService.relateQuery(request);
        return RestResult.success(responseList);
    }
}
