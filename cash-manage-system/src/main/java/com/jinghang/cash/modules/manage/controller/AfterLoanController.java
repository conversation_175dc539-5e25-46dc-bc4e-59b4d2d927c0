package com.jinghang.cash.modules.manage.controller;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jinghang.cash.enums.PictureType;
import com.jinghang.cash.enums.ResultCode;
import com.jinghang.cash.exception.BadRequestException;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.service.AfterLoanService;
import com.jinghang.cash.modules.manage.service.OfflineRepayApplyService;
import com.jinghang.cash.modules.manage.service.OfflineRepayReduceService;
import com.jinghang.cash.modules.manage.vo.QueryAggregatePaymentVo;
import com.jinghang.cash.modules.manage.vo.req.AggregatePayApplyReq;
import com.jinghang.cash.modules.manage.vo.req.AuditReq;
import com.jinghang.cash.modules.manage.vo.req.OfflineRepayApplyRequest;
import com.jinghang.cash.modules.manage.vo.req.OfflineRepayReduceReq;
import com.jinghang.cash.modules.manage.vo.req.QueryAggregatePaymentReq;
import com.jinghang.cash.modules.manage.vo.req.QueryRepayPlanReq;
import com.jinghang.cash.modules.manage.vo.req.ReduceApplyReq;
import com.jinghang.cash.modules.manage.vo.req.RepayApplyPreviewReq;
import com.jinghang.cash.modules.manage.vo.req.RepayApplyReq;
import com.jinghang.cash.modules.manage.vo.req.SendPayUrlSmsReq;
import com.jinghang.cash.modules.manage.vo.req.TrialReq;
import com.jinghang.cash.modules.manage.vo.res.OfflineRepayApplyResponse;
import com.jinghang.cash.modules.manage.vo.res.OfflineRepayReduceResponse;
import com.jinghang.cash.modules.manage.vo.rsp.QueryRepayPlanResp;
import com.jinghang.cash.modules.manage.vo.rsp.RepayApplyPreviewRsp;
import com.jinghang.cash.modules.manage.vo.rsp.TrialRes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 贷后管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/afterLoan")
public class AfterLoanController {

    @Autowired
    private AfterLoanService afterLoanService;

    @Autowired
    private OfflineRepayApplyService offlineRepayApplyService;

    @Autowired
    private OfflineRepayReduceService offlineRepayReduceService;

    /**
     * 查询还款计划
     *
     * @return
     */
    @PostMapping("/queryRepayPlan")
    public RestResult<QueryRepayPlanResp> queryRepayPlan(@RequestBody QueryRepayPlanReq req) {
        if (StringUtils.isAllBlank(req.getOrderId(), req.getMobile(), req.getCertNo())) {
            throw new RuntimeException("缺少查询参数");
        }
        QueryRepayPlanResp res = afterLoanService.queryRepayPlan(req);
        return RestResult.success(res);
    }


    /**
     * 试算
     *
     * @return
     */
    @PostMapping("/trial")
    public RestResult<TrialRes> trial(@RequestBody @Validated TrialReq req) {
        TrialRes res = afterLoanService.trial(req);
        return RestResult.success(res);
    }


    /**
     * 减免申请
     *
     * @return
     */
    @PostMapping("/reduceApply")
    public RestResult<Object> reduceApply(@RequestBody @Validated ReduceApplyReq req) {
        if (req.getReduceAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BadRequestException("减免金额必须要>0");
        }
        afterLoanService.reduceApply(req);
        return RestResult.success();
    }

    /**
     * 还款销账预览/聚合支付预览
     *
     * @return
     */
    @PostMapping("/repayApplyPreview")
    public RestResult<RepayApplyPreviewRsp> repayApplyPreview(@RequestBody @Validated RepayApplyPreviewReq req) {
        RepayApplyPreviewRsp res = afterLoanService.repayApplyPreview(req);
        return RestResult.success(res);
    }

    /**
     * 还款销账
     *
     * @return
     */
    @PostMapping("/repayApply")
    public RestResult<Object> repayApply(@RequestPart("req") @Validated RepayApplyReq req, @RequestPart("file") MultipartFile file) {
        if (file.isEmpty()) {
            return RestResult.fail(ResultCode.FILE_IS_EMPTY);
        }
        String suffix = file.getOriginalFilename().split("\\.")[1];
        if (!(PictureType.jpg.name().equals(suffix) || PictureType.png.name().equals(suffix))) {
            return RestResult.fail(ResultCode.PICTURE_NOT_JPG_PNG);
        }
        // 校验入参实还金额不能超过两位小数
        if (req.getActAmount().scale() > 2) {
            throw new BadRequestException("实还金额不能超过两位小数");
        }
        afterLoanService.repayApply(req, file);
        return RestResult.success();
    }

    /**
     * 后台销账记录-列表分页查询
     */
    @PostMapping("/queryRepayApplyPage")
    public RestResult<PageInfo<OfflineRepayApplyResponse>> queryRepayApplyPage(@Validated @RequestBody OfflineRepayApplyRequest reqVO) {
        PageInfo<OfflineRepayApplyResponse> result = offlineRepayApplyService.searchPage(reqVO);
        return RestResult.success(result);
    }


    /**
     * 后台减免订单-列表分页查询
     */
    @PostMapping("/queryRepayReducePage")
    public RestResult<PageInfo<OfflineRepayReduceResponse>> queryRepayReducePage(@Validated @RequestBody OfflineRepayReduceReq req) {
        PageInfo<OfflineRepayReduceResponse> result = offlineRepayReduceService.searchPage(req);
        return RestResult.success(result);
    }

    /**
     * 后台减免订单-审核
     */
    @PostMapping("/audit")
    public RestResult<ResultCode> audit(@Validated @RequestBody AuditReq req) {
        offlineRepayReduceService.audit(req);
        return RestResult.success(ResultCode.SUCCESS);
    }



    /**
     * 聚合支付记录查询
     */
    @PostMapping("/queryAggregatePayPage")
    public RestResult<PageInfo<QueryAggregatePaymentVo>> queryAggregatePaymentPage(@Validated @RequestBody QueryAggregatePaymentReq req) {

        if(true){
            PageInfo<QueryAggregatePaymentVo> pageInfo = new PageInfo<>();

            QueryAggregatePaymentVo queryAggregatePaymentVo = new QueryAggregatePaymentVo();
            queryAggregatePaymentVo.setId("111");
            queryAggregatePaymentVo.setOrderId("111");
            queryAggregatePaymentVo.setName("111");
            queryAggregatePaymentVo.setMobile("111");
            queryAggregatePaymentVo.setApplyState("111");
            queryAggregatePaymentVo.setWriteOffStatus("111");
            queryAggregatePaymentVo.setApplyTime(LocalDateTime.now());
            queryAggregatePaymentVo.setPayTime(LocalDateTime.now());
            queryAggregatePaymentVo.setPayResult("INIT");
            queryAggregatePaymentVo.setPeriod(3);
            queryAggregatePaymentVo.setAmount(new BigDecimal(1000));
            queryAggregatePaymentVo.setReduceAmount(new BigDecimal(10));
            queryAggregatePaymentVo.setActAmount(new BigDecimal(1000));
            queryAggregatePaymentVo.setPayChannel("BF");
            queryAggregatePaymentVo.setProductChannel("BF");
            queryAggregatePaymentVo.setRemark("BF");
            queryAggregatePaymentVo.setPayUrl("BF");
            queryAggregatePaymentVo.setOuterOrderNo("BF");
            queryAggregatePaymentVo.setMerchantOutOrderNo("BF");
            queryAggregatePaymentVo.setPayerAcctCode("BF");
            queryAggregatePaymentVo.setPayerUserName("BF");
            queryAggregatePaymentVo.setOuterTradeNo("BF");

            pageInfo.setList(Lists.newArrayList(queryAggregatePaymentVo));
            return RestResult.success(pageInfo);
        }
//        PageInfo<QueryAggregatePaymentVo> result =  afterLoanService.queryAggregatePaymentPage(req);
        return null;
    }


    /**
     * 发送支付链接短信
     */
    @PostMapping("/sendPayUrlSms")
    public RestResult<Void> sendPayUrlSms(@Validated @RequestBody SendPayUrlSmsReq req) {
       // afterLoanService.sendPayUrlSms(req);
        return RestResult.success();
    }

    /**
     * 下载聚合支付excel表格
     * @return
     */
    @PostMapping("/downloadExcel")
    public void downloadExcel(@Validated @RequestBody QueryAggregatePaymentReq req,
                                          HttpServletResponse response) throws IOException {
        afterLoanService.downloadExcel(req, response);
    }
}

