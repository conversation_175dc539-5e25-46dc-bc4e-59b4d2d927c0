package com.jinghang.cash.modules.manage.vo.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class FlowBlackListReq {
    /**
     * 手机号列表，多个手机号以英文逗号分隔
     */
    @NotBlank(message = "流量渠道不能为空")    // 不能为空
    private String flowChanel;
    /**
     * 截止日期，格式为yyyy-MM-dd
     */
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式应为yyyy-MM-dd")
    private String deadlineTime;

    private String reason;
}
