package com.jinghang.cash.modules.manage.vo.res;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.enums.FlowCapitalEnable;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FlowRouteConfigPageResponse implements Serializable {

    private static final long serialVersionUID = 8410988783857781415L;

    /**
     * 流量id
     */
    private String id;
    /**
     * 渠道方
     */
    private String flowChannel;
    /**
     * 渠道名字
     */
    private String flowChannelName;

    /**
     * 资金方
     */
    private String capitalConfigStr;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;
    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
    /**
     * 流量启用状态
     */
    private FlowCapitalEnable flowEnabled;

    /**
     * 渠道方名字
     */
    private String desc;

    private List<CapitalConfigDTO> capitalConfigList;

    @Data
    public static class CapitalConfigDTO {

        private String capitalConfigStr;

        private String capitalCode;

        private String enabled;
    }

}
