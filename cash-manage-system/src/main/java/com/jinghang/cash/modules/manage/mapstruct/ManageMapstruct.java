package com.jinghang.cash.modules.manage.mapstruct;

import com.jinghang.cash.modules.manage.vo.PreOrderVo;
import com.jinghang.cash.modules.manage.vo.req.CapitalConfigRequest;
import com.jinghang.cash.modules.manage.vo.req.FlowRouteConfigRequest;
import com.jinghang.cash.modules.manage.vo.req.PreOrderReq;
import com.jinghang.cash.modules.manage.vo.res.BankChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigInfoResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowProtocolChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.OfflineRepayApplyResponse;
import com.jinghang.cash.modules.manage.vo.res.PaymentChannelConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyRelateResponse;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.RightsBasePackageResponse;
import com.jinghang.cash.modules.manage.vo.rsp.PreOrderResp;
import com.jinghang.cash.pojo.*;
import com.jinghang.cash.pojo.FlowConfigSlave;

import com.jinghang.ppd.api.dto.route.CapitalConfigDTO;
import com.jinghang.ppd.api.dto.route.FlowRouteConfigDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ManageMapstruct {
    ManageMapstruct INSTANCE = Mappers.getMapper(ManageMapstruct.class);

    OfflineRepayApplyResponse copyOfflineRepayApplyToOfflineRepayApplyResponse(OfflineRepayApply offlineRepayApply);

    @AfterMapping
    static void afterCopyOfflineRepayApplyToOfflineRepayApplyResponse(OfflineRepayApply offlineRepayApply, @MappingTarget OfflineRepayApplyResponse offlineRepayApplyResponse) {
        //溢出金额
        BigDecimal overflowAmount = BigDecimal.ZERO;

        BigDecimal actAmount = offlineRepayApply.getActAmount();
        BigDecimal amount = offlineRepayApply.getAmount();
        if (actAmount != null && amount != null) {
            BigDecimal money = actAmount.subtract(amount);
            if (money.compareTo(BigDecimal.ZERO) > 0) {
                overflowAmount = money;
            }
        }
        offlineRepayApplyResponse.setOverflowAmount(overflowAmount);
    }

    List<CapitalConfigResponse> copyCapitalConfigsToCapitalConfigResponses(List<CapitalConfigSlave> list);

    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "creditTimeStatus", source = "creditTimeStatus")
    @Mapping(target = "loanTimeStatus", source = "loanTimeStatus")
    @Mapping(target = "repayTimeStatus", source = "repayTimeStatus")
    CapitalConfigSlave copyCapitalConfigRequestToCapitalConfig(CapitalConfigRequest reqVO);

    CapitalConfigInfoResponse copyCapitalConfigToCapitalConfigInfoResponse(CapitalConfigSlave entity);

    List<FlowRouteConfigResponse> copyCapitalConfigToFlowRouteConfigResponse(List<CapitalConfigSlave> list);

    @Mapping(target = "capitalId", source = "id")
    @Mapping(target = "selected", ignore = true, defaultValue = "false")
    @Mapping(target = "id", ignore = true)
    FlowRouteConfigResponse copyCapitalConfigToFlowRouteConfigResponse(CapitalConfigSlave capitalConfigSlave);

    @Mapping(target = "bankRate", source = "bankRate")
    CapitalConfigDTO copyEntityToCapitalConfigDTO(CapitalConfigSlave capitalConfigSlave);

    List<BankChannelResponse> copyFlowConfigToBankChannelResponse(List<FlowConfigSlave> entityList);

    @Mapping(target = "name", source = "flowChannel")
    BankChannelResponse copyFlowConfigToBankChannelResponse(FlowConfigSlave entity);

    FlowRouteConfigDTO copyFlowRouteConfigRequestToFlowRouteConfigDTO(FlowRouteConfigRequest reqVO);

    List<BankChannelResponse> copyCapitalConfigToBankChannelResponse(List<CapitalConfigSlave> list);

    @Mapping(target = "name", source = "bankChannel")
    @Mapping(target = "id", source = "id")
    BankChannelResponse copyCapitalConfigToBankChannelResponse(CapitalConfigSlave capitalConfigSlave);


    List<RightsBasePackageResponse> copyRightsBasePackageToVO(List<RightsBasePackage> list);

    RightsBasePackageResponse copyRightsBasePackageToVO(RightsBasePackage rightsConfig);


    List<RightsBasePackageResponse> copyRightsConfigToVOS(List<RightsConfig> list);

    RightsBasePackageResponse copyRightsConfigToVO(RightsConfig entity);



    PreOrderVo toPreOrderVo(PreOrderReq req);

    PreOrderResp toPreOrderResp(PreOrderVo vo);


    PaymentChannelConfigResponse toPaymentChannelConfigResponse(PaymentChannelConfig config);

    List<PaymentChannelConfigResponse> toPaymentChannelConfigResponseList(List<PaymentChannelConfig> list);

    List<FlowProtocolChannelResponse> toFlowProtocolChannelList(List<FlowConfigSlave> configList);

    FlowProtocolChannelResponse toFlowProtocolChannelDetail(FlowConfigSlave flowConfigSlave);

    @Mapping(target = "operator", source = "updatedBy")
    @Mapping(target = "modifyTime", source = "updatedTime")
    RevolvingStrategyConfigResponse toRevolvingStrategyConfigResponse(RevolvingStrategyConfig config);

    List<RevolvingStrategyConfigResponse> toRevolvingStrategyConfigResponseList(List<RevolvingStrategyConfig> list);

    RevolvingStrategyRelateResponse toRevolvingStrategyRelateResponse(RevolvingStrategyRelate revolvingStrategyRelate);

    List<RevolvingStrategyRelateResponse> toRevolvingStrategyRelateResponseList(List<RevolvingStrategyRelate> revolvingStrategyRelateList);

    @Mapping(target = "creditTimeStatus", source = "creditTimeStatus")
    @Mapping(target = "loanTimeStatus", source = "loanTimeStatus")
    @Mapping(target = "repayTimeStatus", source = "repayTimeStatus")
    CapitalConfigSlave copyCapitalConfigDTOToCapitalConfig(CapitalConfigDTO reqDTO);
}
