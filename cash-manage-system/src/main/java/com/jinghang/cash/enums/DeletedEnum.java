package com.jinghang.cash.enums;


public enum DeletedEnum {
    Y("删除"),
    N("正常");
    private String desc;

    DeletedEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }


    public static DeletedEnum getEnum(String desc) {
        for (DeletedEnum channel : DeletedEnum.values()) {
            if (channel.getDesc().equals(desc)) {
                return channel;
            }
        }
        return null;
    }

}
