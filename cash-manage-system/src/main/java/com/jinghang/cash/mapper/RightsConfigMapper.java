package com.jinghang.cash.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.modules.manage.vo.res.RightsBasePackageResponse;
import com.jinghang.cash.pojo.RightsConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【rights_config(权益配置)】的数据库操作Mapper
* @createDate 2024-04-18 21:35:29
* @Entity me.zhengjie.entity.RightsConfig
*/
@Mapper
@DS("slave")
public interface RightsConfigMapper extends BaseMapper<RightsConfig> {

    List<RightsBasePackageResponse> queryRightsPage();
}




