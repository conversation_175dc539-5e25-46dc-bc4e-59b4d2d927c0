package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import com.jinghang.cash.modules.manage.vo.req.LoanPreFailPageRequest;
import com.jinghang.cash.modules.manage.vo.res.LoanPreFailPageResponse;
import com.jinghang.cash.pojo.LoanFailFollow;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("slave")
public interface LoanFailFollowMapper extends BaseMapper<LoanFailFollow> {
    Page<LoanPreFailPageResponse> queryPreFail(LoanPreFailPageRequest req);
}
