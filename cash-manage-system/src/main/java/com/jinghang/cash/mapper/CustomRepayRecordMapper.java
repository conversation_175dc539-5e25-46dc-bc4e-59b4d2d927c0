package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jinghang.cash.pojo.CustomRepayRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【custom_repay_record(对客还款记录)】的数据库操作Mapper
* @createDate 2023-11-16 16:11:51
* @Entity com.jinghang.cash.pojo.CustomRepayRecord
*/
@Mapper
@DS("slave")
public interface CustomRepayRecordMapper extends BaseMapper<CustomRepayRecord> {
    CustomRepayRecord findLatestFailRepayRecord(String loanId, Integer period);

}




